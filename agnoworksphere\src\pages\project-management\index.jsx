import React, { useState, useEffect } from 'react';
import RoleBasedHeader from '../../components/ui/RoleBasedHeader';
// import Sidebar from '../../components/ui/Sidebar';
import Breadcrumb from '../../components/ui/Breadcrumb';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import ProjectOverview from './components/ProjectOverview';
import TasksTab from './components/TasksTab';
import SettingsTab from './components/SettingsTab';
import authService from '../../utils/authService';

const ProjectManagement = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [sidebarExpanded, setSidebarExpanded] = useState(true);

  // Authentication state
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState('member');
  const [currentOrganization, setCurrentOrganization] = useState(null);

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'LayoutDashboard',
      description: 'Project summary and key metrics'
    },
    {
      id: 'tasks',
      label: 'Tasks',
      icon: 'CheckSquare',
      description: 'Task management and tracking'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: 'Settings',
      description: 'Project configuration and permissions'
    }
  ];

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <ProjectOverview />;
      case 'tasks':
        return <TasksTab />;
      case 'settings':
        return <SettingsTab />;
      default:
        return <ProjectOverview />;
    }
  };

  const getActiveTabInfo = () => {
    return tabs.find(tab => tab.id === activeTab) || tabs[0];
  };

  // Load user authentication data
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const userResponse = await authService.getCurrentUser();
        const orgResponse = await authService.getCurrentOrganization();

        if (userResponse.data && userResponse.data.user) {
          setCurrentUser(userResponse.data.user);
          setUserRole(userResponse.data.user.role || 'member');
        }

        if (orgResponse.data && orgResponse.data.organization) {
          setCurrentOrganization(orgResponse.data.organization);
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        setUserRole('member');
      }
    };

    loadUserData();
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <RoleBasedHeader
        userRole={userRole.toLowerCase()}
        currentUser={currentUser ? {
          name: `${currentUser.firstName} ${currentUser.lastName}`,
          email: currentUser.email,
          avatar: currentUser.avatar || '/assets/images/avatar.jpg',
          role: userRole
        } : {
          name: 'Loading...',
          email: '',
          avatar: '/assets/images/avatar.jpg',
          role: userRole
        }}
        currentOrganization={currentOrganization}
      />
      {/* <Sidebar /> */}
      
      <main className={`transition-all duration-300 ${sidebarExpanded ? 'ml-60' : 'ml-16'} pt-16`}>
        <div className="p-6 max-w-7xl mx-auto">
          <Breadcrumb />
          
          {/* Page Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8">
            <div>
              <h1 className="text-3xl font-bold text-foreground mb-2">Project Management</h1>
              <p className="text-text-secondary">
                Comprehensive project oversight and administrative controls for effective team coordination
              </p>
            </div>
            <div className="flex gap-3">
              <Button variant="outline" iconName="Download" iconPosition="left">
                Export Data
              </Button>
              <Button variant="outline" iconName="BarChart3" iconPosition="left">
                Generate Report
              </Button>
              <Button variant="default" iconName="Plus" iconPosition="left">
                New Project
              </Button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-card rounded-lg border border-border mb-6">
            {/* Desktop Tab Navigation */}
            <div className="hidden md:flex border-b border-border">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`flex items-center gap-3 px-6 py-4 font-medium transition-colors relative ${
                    activeTab === tab.id
                      ? 'text-primary border-b-2 border-primary bg-primary/5' :'text-text-secondary hover:text-foreground hover:bg-muted/50'
                  }`}
                >
                  <Icon name={tab.icon} size={18} />
                  <div className="text-left">
                    <div className="font-medium">{tab.label}</div>
                    <div className="text-xs text-text-secondary">{tab.description}</div>
                  </div>
                </button>
              ))}
            </div>

            {/* Mobile Tab Navigation */}
            <div className="md:hidden border-b border-border p-4">
              <div className="relative">
                <select
                  value={activeTab}
                  onChange={(e) => handleTabChange(e.target.value)}
                  className="w-full appearance-none bg-background border border-border rounded-lg px-4 py-3 pr-10 text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  {tabs.map((tab) => (
                    <option key={tab.id} value={tab.id}>
                      {tab.label} - {tab.description}
                    </option>
                  ))}
                </select>
                <Icon 
                  name="ChevronDown" 
                  size={20} 
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-secondary pointer-events-none" 
                />
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {/* Active Tab Header */}
              <div className="flex items-center gap-3 mb-6 md:hidden">
                <Icon name={getActiveTabInfo().icon} size={24} className="text-primary" />
                <div>
                  <h2 className="text-xl font-semibold text-foreground">{getActiveTabInfo().label}</h2>
                  <p className="text-sm text-text-secondary">{getActiveTabInfo().description}</p>
                </div>
              </div>

              {/* Render Active Tab Content */}
              {renderTabContent()}
            </div>
          </div>

          {/* Quick Actions Footer */}
          <div className="bg-card rounded-lg border border-border p-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="flex items-center gap-3">
                <Icon name="Zap" size={20} className="text-primary" />
                <div>
                  <h3 className="font-medium text-foreground">Quick Actions</h3>
                  <p className="text-sm text-text-secondary">Frequently used project management tools</p>
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" iconName="UserPlus" iconPosition="left">
                  Invite Team
                </Button>
                <Button variant="outline" size="sm" iconName="Calendar" iconPosition="left">
                  Schedule Meeting
                </Button>
                <Button variant="outline" size="sm" iconName="FileText" iconPosition="left">
                  Create Template
                </Button>
                <Button variant="outline" size="sm" iconName="Archive" iconPosition="left">
                  Backup Project
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default ProjectManagement;