import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import RoleBasedHeader from '../../components/ui/RoleBasedHeader';
import DashboardHeader from './components/DashboardHeader';
import KPICard from './components/KPICard';
import ProjectCard from './components/ProjectCard';
import ActivityFeed from './components/ActivityFeed';
import QuickActions from './components/QuickActions';
import TaskSummary from './components/TaskSummary';
import TeamOverview from './components/TeamOverview';
import NotificationPanel from './components/NotificationPanel';
import authService from '../../utils/authService';
import apiService from '../../utils/apiService';
import CreateProjectModal from '../../components/modals/CreateProjectModal';
import InviteMemberModal from '../../components/modals/InviteMemberModal';

const RoleBasedDashboard = () => {
  const location = useLocation();
  const [userRole, setUserRole] = useState('member'); // Default role
  const [searchValue, setSearchValue] = useState('');
  const [filterValue, setFilterValue] = useState('all');
  const [dashboardData, setDashboardData] = useState(null);
  const [projects, setProjects] = useState([]);
  const [currentUser, setCurrentUser] = useState(null);
  const [organizations, setOrganizations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showWelcome, setShowWelcome] = useState(false);
  const [welcomeMessage, setWelcomeMessage] = useState('');
  const [showCreateProject, setShowCreateProject] = useState(false);
  const [showInviteMember, setShowInviteMember] = useState(false);

  // Load dashboard data from backend
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);

        // Check for welcome message from navigation state
        if (location.state?.message && location.state?.type === 'success') {
          setWelcomeMessage(location.state.message);
          setShowWelcome(true);
          // Clear the state to prevent showing on refresh
          window.history.replaceState({}, document.title);
        }

        // Get current user and role
        const userResult = await authService.getCurrentUser();
        if (userResult.data.user) {
          setCurrentUser(userResult.data.user);
          setUserRole(userResult.data.user.role || authService.getUserRole());
          setOrganizations(userResult.data.organizations || []);
        } else {
          // If no user, redirect to login
          window.location.href = '/login';
          return;
        }

        // Get dashboard stats
        const statsResult = await authService.getDashboardStats();
        if (statsResult.data) {
          setDashboardData(statsResult.data);
        }

        // Get projects
        const organizationId = authService.getOrganizationId();
        if (organizationId) {
          const projectsResult = await apiService.projects.getAll(organizationId);
          setProjects(projectsResult || []);
        }

        setError(null);
      } catch (err) {
        console.error('Failed to load dashboard data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, [location.state]);

  // Project creation handler
  const handleCreateProject = async (projectData) => {
    try {
      const organizationId = authService.getOrganizationId();
      if (!organizationId) {
        throw new Error('No organization found');
      }

      const newProject = await apiService.projects.create(organizationId, projectData);

      // Refresh projects list
      const projectsResult = await apiService.projects.getAll(organizationId);
      setProjects(projectsResult || []);

      // Refresh dashboard stats
      const statsResult = await authService.getDashboardStats();
      if (statsResult.data) {
        setDashboardData(statsResult.data);
      }

      console.log('Project created successfully:', newProject);
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error;
    }
  };

  // Quick action handlers
  const handleOpenCreateProject = () => {
    setShowCreateProject(true);
  };

  const handleCloseCreateProject = () => {
    setShowCreateProject(false);
  };

  const handleManageUsers = () => {
    // Navigate to team members page
    window.location.href = '/team-members';
  };

  const handleInviteMembers = () => {
    setShowInviteMember(true);
  };

  const handleCloseInviteMember = () => {
    setShowInviteMember(false);
  };

  const handleSendInvitation = async (inviteData) => {
    try {
      const organizationId = authService.getOrganizationId();
      if (!organizationId) {
        throw new Error('No organization found');
      }

      // Call the backend API to send invitation
      const response = await fetch(`http://localhost:3001/api/v1/organizations/${organizationId}/invite`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authService.getAccessToken()}`
        },
        body: JSON.stringify({
          email: inviteData.email,
          role: inviteData.role
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to send invitation');
      }

      const result = await response.json();
      console.log('Invitation sent successfully:', result);

      // Refresh dashboard stats to show updated member count
      const statsResult = await authService.getDashboardStats();
      if (statsResult.data) {
        setDashboardData(statsResult.data);
      }

    } catch (error) {
      console.error('Failed to send invitation:', error);
      throw error;
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-red-600 text-lg mb-2">Failed to load dashboard</p>
          <p className="text-slate-600 text-sm">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Mock data for different dashboard components (fallback)
  const mockProjects = [
    {
      id: 1,
      name: "E-commerce Platform Redesign",
      description: "Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.",
      status: "Active",
      priority: "High",
      progress: 75,
      dueDate: "Dec 15, 2025",
      team: [
        { name: "Sarah Johnson", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150" },
        { name: "Mike Chen", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150" },
        { name: "Emily Davis", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150" },
        { name: "Alex Rodriguez", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150" }
      ]
    },
    {
      id: 2,
      name: "Mobile App Development",
      description: "Native iOS and Android application development for customer engagement and loyalty program management.",
      status: "Active",
      priority: "Medium",
      progress: 45,
      dueDate: "Jan 30, 2026",
      team: [
        { name: "David Kim", avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150" },
        { name: "Lisa Wang", avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150" },
        { name: "Tom Wilson", avatar: "https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150" }
      ]
    },
    {
      id: 3,
      name: "Data Analytics Dashboard",
      description: "Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.",
      status: "Completed",
      priority: "Low",
      progress: 100,
      dueDate: "Nov 20, 2025",
      team: [
        { name: "Rachel Green", avatar: "https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150" },
        { name: "James Brown", avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150" }
      ]
    }
  ];

  const mockActivities = [
    {
      id: 1,
      type: "task_completed",
      user: { name: "Sarah Johnson", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150" },
      description: "Completed the user interface mockups for the checkout process",
      project: "E-commerce Platform",
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      isPublic: true
    },
    {
      id: 2,
      type: "comment_added",
      user: { name: "Mike Chen", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150" },
      description: "Added feedback on the mobile responsive design implementation",
      project: "E-commerce Platform",
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      isPublic: true
    },
    {
      id: 3,
      type: "project_created",
      user: { name: "Emily Davis", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150" },
      description: "Created new project for Q1 marketing campaign automation",
      project: "Marketing Automation",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      isPublic: false
    },
    {
      id: 4,
      type: "member_added",
      user: { name: "Alex Rodriguez", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150" },
      description: "Joined the mobile app development team as a senior developer",
      project: "Mobile App Development",
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      isPublic: true
    }
  ];

  const mockTasks = [
    {
      id: 1,
      title: "Implement payment gateway integration",
      status: "In Progress",
      priority: "High",
      dueDate: "Dec 10, 2025",
      assignee: "Sarah Johnson",
      project: "E-commerce Platform"
    },
    {
      id: 2,
      title: "Design user onboarding flow",
      status: "To Do",
      priority: "Medium",
      dueDate: "Dec 12, 2025",
      assignee: "Mike Chen",
      project: "Mobile App"
    },
    {
      id: 3,
      title: "Set up automated testing pipeline",
      status: "Review",
      priority: "High",
      dueDate: "Dec 8, 2025",
      assignee: "Emily Davis",
      project: "E-commerce Platform"
    },
    {
      id: 4,
      title: "Create API documentation",
      status: "Done",
      priority: "Low",
      dueDate: "Dec 5, 2025",
      assignee: "Alex Rodriguez",
      project: "Data Analytics"
    },
    {
      id: 5,
      title: "Optimize database queries",
      status: "Blocked",
      priority: "High",
      dueDate: "Dec 15, 2025",
      assignee: "David Kim",
      project: "E-commerce Platform"
    }
  ];

  const mockTeamMembers = [
    {
      id: 1,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      role: "Admin",
      status: "Online",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150",
      department: "Design",
      currentTask: "UI/UX Design Review",
      tasksCompleted: 24,
      lastActive: new Date()
    },
    {
      id: 2,
      name: "Mike Chen",
      email: "<EMAIL>",
      role: "Member",
      status: "Away",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150",
      department: "Development",
      currentTask: "Frontend Implementation",
      tasksCompleted: 18,
      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: 3,
      name: "Emily Davis",
      email: "<EMAIL>",
      role: "Owner",
      status: "Online",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150",
      department: "Management",
      currentTask: "Project Planning",
      tasksCompleted: 32,
      lastActive: new Date()
    },
    {
      id: 4,
      name: "Alex Rodriguez",
      email: "<EMAIL>",
      role: "Member",
      status: "Busy",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150",
      department: "Development",
      currentTask: "API Integration",
      tasksCompleted: 15,
      lastActive: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: 5,
      name: "David Kim",
      email: "<EMAIL>",
      role: "Viewer",
      status: "Offline",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150",
      department: "QA",
      currentTask: null,
      tasksCompleted: 8,
      lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)
    }
  ];

  const mockNotifications = [
    {
      id: 1,
      type: "task_assigned",
      title: "New Task Assigned",
      message: "You have been assigned to work on the payment gateway integration for the e-commerce platform.",
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      read: false,
      priority: "high",
      user: { name: "Emily Davis", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150" },
      actions: [
        { label: "View Task", variant: "default" },
        { label: "Accept", variant: "outline" }
      ]
    },
    {
      id: 2,
      type: "deadline_reminder",
      title: "Deadline Approaching",
      message: "The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.",
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      read: false,
      priority: "medium",
      user: null,
      actions: [
        { label: "View Details", variant: "outline" }
      ]
    },
    {
      id: 3,
      type: "comment_mention",
      title: "You were mentioned",
      message: "Sarah Johnson mentioned you in a comment on the mobile app development project.",
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      read: true,
      priority: "low",
      user: { name: "Sarah Johnson", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150" },
      actions: [
        { label: "Reply", variant: "outline" }
      ]
    },
    {
      id: 4,
      type: "system_alert",
      title: "System Maintenance",
      message: "Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.",
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
      read: true,
      priority: "medium",
      user: null,
      actions: []
    }
  ];

  // Role-based KPI data with real data integration
  const getKPIData = () => {
    const realData = dashboardData || {};

    switch (userRole) {
      case 'owner':
        return [
          { title: "Organizations", value: realData.total_organizations?.toString() || "1", change: "+0", changeType: "neutral", icon: "Building", color: "success" },
          { title: "Active Projects", value: realData.total_projects?.toString() || "0", change: "+0", changeType: "neutral", icon: "FolderOpen", color: "primary" },
          { title: "Team Members", value: realData.total_members?.toString() || "1", change: "+0", changeType: "neutral", icon: "Users", color: "accent" },
          { title: "Recent Activity", value: realData.recent_activity?.length?.toString() || "0", change: "+0", changeType: "neutral", icon: "Activity", color: "warning" }
        ];
      case 'admin':
        return [
          { title: "Active Projects", value: realData.total_projects?.toString() || "0", change: "+0", changeType: "neutral", icon: "FolderOpen", color: "primary" },
          { title: "Team Members", value: realData.total_members?.toString() || "1", change: "+0", changeType: "neutral", icon: "Users", color: "success" },
          { title: "Organizations", value: realData.total_organizations?.toString() || "1", change: "+0", changeType: "neutral", icon: "Building", color: "accent" },
          { title: "Recent Activity", value: realData.recent_activity?.length?.toString() || "0", change: "+0", changeType: "neutral", icon: "Activity", color: "warning" }
        ];
      case 'member':
        return [
          { title: "My Projects", value: realData.total_projects?.toString() || "0", change: "+0", changeType: "neutral", icon: "FolderOpen", color: "primary" },
          { title: "Organizations", value: realData.total_organizations?.toString() || "1", change: "+0", changeType: "neutral", icon: "Building", color: "success" },
          { title: "Team Members", value: realData.total_members?.toString() || "1", change: "+0", changeType: "neutral", icon: "Users", color: "accent" },
          { title: "Recent Activity", value: realData.recent_activity?.length?.toString() || "0", change: "+0", changeType: "neutral", icon: "Activity", color: "warning" }
        ];
      case 'viewer':
        return [
          { title: "Projects Viewed", value: realData.total_projects?.toString() || "0", change: "+0", changeType: "neutral", icon: "Eye", color: "primary" },
          { title: "Organizations", value: realData.total_organizations?.toString() || "1", change: "+0", changeType: "neutral", icon: "Building", color: "accent" },
          { title: "Team Members", value: realData.total_members?.toString() || "1", change: "+0", changeType: "neutral", icon: "Users", color: "success" },
          { title: "Activity Items", value: realData.recent_activity?.length?.toString() || "0", change: "+0", changeType: "neutral", icon: "BarChart3", color: "warning" }
        ];
      default:
        return [
          { title: "Projects", value: "0", change: "+0", changeType: "neutral", icon: "FolderOpen", color: "primary" },
          { title: "Organizations", value: "0", change: "+0", changeType: "neutral", icon: "Building", color: "success" },
          { title: "Members", value: "0", change: "+0", changeType: "neutral", icon: "Users", color: "accent" },
          { title: "Activity", value: "0", change: "+0", changeType: "neutral", icon: "Activity", color: "warning" }
        ];
    }
  };

  // Filter projects based on search, filter values, and role-based access
  const filteredProjects = (projects.length > 0 ? projects : mockProjects).filter(project => {
    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) ||
                         (project.description || '').toLowerCase().includes(searchValue.toLowerCase());
    const matchesFilter = filterValue === 'all' ||
                         (project.status || 'active').toLowerCase() === filterValue.toLowerCase();

    // Role-based project access control
    const hasProjectAccess = () => {
      if (!currentUser?.role) return true; // Default access if role not set

      const userRole = currentUser.role.toLowerCase();

      // Viewers should only see projects they are specifically invited to
      if (userRole === 'viewer') {
        // For now, viewers can see projects from their organization
        // In a real implementation, this would check project-specific invitations
        return project.organization_id === currentUser.organization_id;
      }

      // Members, admins, and owners can see all projects in their organization
      return true;
    };

    return matchesSearch && matchesFilter && hasProjectAccess();
  });



  // Get current organization data
  const currentOrganization = organizations.length > 0 ? {
    name: organizations[0].organization?.name || 'Your Organization',
    domain: currentUser?.email?.split('@')[1] || 'company.com',
    logo: organizations[0].organization?.logo_url || '/assets/images/org-logo.png'
  } : {
    name: 'Your Organization',
    domain: 'company.com',
    logo: '/assets/images/org-logo.png'
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20">
      {/* Role-Based Header */}
      <RoleBasedHeader
        userRole={userRole.toLowerCase()}
        currentUser={currentUser ? {
          name: `${currentUser.firstName} ${currentUser.lastName}`,
          email: currentUser.email,
          avatar: currentUser.avatar || '/assets/images/avatar.jpg',
          role: userRole
        } : {
          name: 'Loading...',
          email: '',
          avatar: '/assets/images/avatar.jpg',
          role: userRole
        }}
        currentOrganization={currentOrganization}
      />

      {/* Welcome Message for New Users */}
      {showWelcome && welcomeMessage && (
        <div className="bg-gradient-to-r from-green-50 to-blue-50 border-l-4 border-green-400 p-6 mt-16 mx-4 rounded-lg shadow-sm">
          <div className="max-w-7xl mx-auto flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-lg font-medium text-green-800">Welcome to Agno WorkSphere!</h3>
              <div className="mt-2 text-sm text-green-700">
                <p>{welcomeMessage}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={() => setShowWelcome(false)}
                  className="bg-green-100 hover:bg-green-200 text-green-800 px-4 py-2 rounded-md text-sm font-medium transition-colors"
                >
                  Got it, thanks!
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* User Info Bar */}
      <div className={`glass-effect border-b border-white/20 p-4 ${showWelcome ? 'mt-4' : 'mt-16'}`}>
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm font-medium text-slate-700">
                {currentUser ? `Welcome, ${currentUser.firstName}!` : 'Loading...'}
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm text-slate-600">
              <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full font-medium capitalize">
                {userRole}
              </span>
              {currentOrganization && (
                <span className="text-slate-500">
                  • {currentOrganization.name}
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center gap-3 text-sm text-slate-600">
            <span>Organizations: {dashboardData?.total_organizations || 0}</span>
            <span>•</span>
            <span>Projects: {dashboardData?.total_projects || 0}</span>
            <span>•</span>
            <span>Members: {dashboardData?.total_members || 0}</span>
          </div>
        </div>
      </div>

      {/* Dashboard Header */}
      <DashboardHeader
        userRole={userRole}
        onFilterChange={setFilterValue}
        onSearchChange={setSearchValue}
        searchValue={searchValue}
      />

      {/* Main Dashboard Content */}
      <div className="max-w-7xl mx-auto p-8">
        {/* Enhanced KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
          {getKPIData().map((kpi, index) => (
            <div key={index} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
              <KPICard
                title={kpi.title}
                value={kpi.value}
                change={kpi.change}
                changeType={kpi.changeType}
                icon={kpi.icon}
                color={kpi.color}
              />
            </div>
          ))}
        </div>

        {/* Main Content Grid with improved spacing */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10">
          {/* Left Column - Projects and Quick Actions */}
          <div className="lg:col-span-2 space-y-8">
            {/* Projects Section with enhanced header */}
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-2xl font-semibold text-slate-800 tracking-tight">
                    {userRole === 'Viewer' ? 'Available Projects' : 'My Projects'}
                  </h2>
                  <p className="text-slate-600 mt-1">Manage and track your active projects</p>
                </div>
                <Link 
                  to="/project-overview-analytics"
                  className="flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200"
                >
                  View Analytics
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Link>
              </div>
              
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                {filteredProjects.map((project, index) => (
                  <div key={project.id} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                    <ProjectCard
                      project={project}
                      userRole={userRole}
                    />
                  </div>
                ))}
              </div>
              
              {filteredProjects.length === 0 && (
                <div className="text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20">
                  <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <p className="text-slate-600 text-lg">No projects match your current filters</p>
                  <p className="text-slate-500 text-sm mt-1">Try adjusting your search or filter criteria</p>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <QuickActions
              userRole={userRole}
              onCreateProject={handleOpenCreateProject}
              onManageUsers={handleManageUsers}
              onInviteMembers={handleInviteMembers}
            />
          </div>

          {/* Right Column - Activity Feed and Notifications */}
          <div className="space-y-8">
            <ActivityFeed activities={mockActivities} userRole={userRole} />
            <NotificationPanel notifications={mockNotifications} userRole={userRole} />
          </div>
        </div>

        {/* Bottom Section - Tasks and Team with improved layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <TaskSummary tasks={mockTasks} userRole={userRole} />
          <TeamOverview teamMembers={mockTeamMembers} userRole={userRole} />
        </div>
      </div>

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={showCreateProject}
        onClose={handleCloseCreateProject}
        onCreateProject={handleCreateProject}
        organizationId={authService.getOrganizationId()}
        organizationName={currentOrganization?.name || 'Your Organization'}
      />

      {/* Invite Member Modal */}
      <InviteMemberModal
        isOpen={showInviteMember}
        onClose={handleCloseInviteMember}
        onInviteMember={handleSendInvitation}
        organizationId={authService.getOrganizationId()}
        organizationName={currentOrganization?.name || 'Your Organization'}
      />
    </div>
  );
};

export default RoleBasedDashboard;