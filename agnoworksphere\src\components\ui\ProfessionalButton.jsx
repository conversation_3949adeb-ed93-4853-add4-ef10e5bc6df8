/**
 * Professional Button Component for Owner Interface
 * Enterprise-grade button with multiple variants and accessibility features
 */
import React from 'react';
import Icon from '../AppIcon';

const ProfessionalButton = ({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  loading = false,
  disabled = false,
  fullWidth = false,
  className = '',
  onClick,
  type = 'button',
  ...props
}) => {
  const baseClasses = `
    inline-flex items-center justify-center font-medium transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 
    disabled:cursor-not-allowed relative overflow-hidden group
  `;

  const variants = {
    primary: `
      bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800
      text-white shadow-lg hover:shadow-xl focus:ring-blue-500
      border border-transparent
    `,
    owner: `
      bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800
      text-white shadow-lg hover:shadow-xl focus:ring-purple-500
      border border-transparent relative
    `,
    secondary: `
      bg-white hover:bg-gray-50 text-gray-700 border border-gray-300
      shadow-sm hover:shadow-md focus:ring-blue-500
    `,
    outline: `
      bg-transparent hover:bg-blue-50 text-blue-600 border border-blue-300
      hover:border-blue-400 focus:ring-blue-500
    `,
    ghost: `
      bg-transparent hover:bg-gray-100 text-gray-600 hover:text-gray-700
      border border-transparent focus:ring-gray-500
    `,
    danger: `
      bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800
      text-white shadow-lg hover:shadow-xl focus:ring-red-500
      border border-transparent
    `,
    success: `
      bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800
      text-white shadow-lg hover:shadow-xl focus:ring-green-500
      border border-transparent
    `
  };

  const sizes = {
    xs: 'px-2.5 py-1.5 text-xs rounded-md gap-1',
    sm: 'px-3 py-2 text-sm rounded-md gap-1.5',
    md: 'px-4 py-2.5 text-sm rounded-lg gap-2',
    lg: 'px-6 py-3 text-base rounded-lg gap-2',
    xl: 'px-8 py-4 text-lg rounded-xl gap-3'
  };

  const iconSizes = {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20
  };

  const classes = `
    ${baseClasses}
    ${variants[variant]}
    ${sizes[size]}
    ${fullWidth ? 'w-full' : ''}
    ${className}
  `;

  const renderIcon = (position) => {
    if (!icon || iconPosition !== position) return null;
    
    return (
      <Icon 
        name={icon} 
        size={iconSizes[size]} 
        className={`${loading ? 'opacity-0' : ''} transition-opacity`}
      />
    );
  };

  const renderLoadingSpinner = () => {
    if (!loading) return null;
    
    return (
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
      </div>
    );
  };

  const renderOwnerGlow = () => {
    if (variant !== 'owner') return null;
    
    return (
      <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-lg"></div>
    );
  };

  return (
    <button
      type={type}
      className={classes}
      onClick={onClick}
      disabled={disabled || loading}
      {...props}
    >
      {renderOwnerGlow()}
      {renderLoadingSpinner()}
      
      <span className={`flex items-center gap-2 ${loading ? 'opacity-0' : ''}`}>
        {renderIcon('left')}
        {children}
        {renderIcon('right')}
      </span>
    </button>
  );
};

// Specialized Owner Action Button
export const OwnerActionButton = ({ children, icon, ...props }) => (
  <ProfessionalButton
    variant="owner"
    icon={icon}
    className="relative group overflow-hidden"
    {...props}
  >
    <span className="relative z-10">{children}</span>
    <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </ProfessionalButton>
);

// Button Group Component
export const ButtonGroup = ({ children, className = '', orientation = 'horizontal' }) => {
  const orientationClasses = {
    horizontal: 'flex flex-row',
    vertical: 'flex flex-col'
  };

  return (
    <div className={`${orientationClasses[orientation]} ${className}`}>
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return child;
        
        const isFirst = index === 0;
        const isLast = index === React.Children.count(children) - 1;
        
        let additionalClasses = '';
        
        if (orientation === 'horizontal') {
          if (!isFirst && !isLast) additionalClasses = 'rounded-none border-l-0';
          else if (isFirst) additionalClasses = 'rounded-r-none';
          else if (isLast) additionalClasses = 'rounded-l-none border-l-0';
        } else {
          if (!isFirst && !isLast) additionalClasses = 'rounded-none border-t-0';
          else if (isFirst) additionalClasses = 'rounded-b-none';
          else if (isLast) additionalClasses = 'rounded-t-none border-t-0';
        }
        
        return React.cloneElement(child, {
          className: `${child.props.className || ''} ${additionalClasses}`.trim()
        });
      })}
    </div>
  );
};

// Icon Button Component
export const IconButton = ({ 
  icon, 
  size = 'md', 
  variant = 'ghost',
  tooltip,
  className = '',
  ...props 
}) => {
  const sizeClasses = {
    xs: 'p-1',
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3',
    xl: 'p-4'
  };

  const iconSizes = {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 20,
    xl: 24
  };

  return (
    <ProfessionalButton
      variant={variant}
      className={`${sizeClasses[size]} ${className}`}
      title={tooltip}
      {...props}
    >
      <Icon name={icon} size={iconSizes[size]} />
    </ProfessionalButton>
  );
};

export default ProfessionalButton;
