"""
AI service for intelligent features and predictions
"""
import logging
import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.models.ai_automation import AIModel, AIPrediction, AIInsight, SmartNotification
from app.models.card import Card
from app.models.project import Project
from app.models.user import User
from app.models.organization import Organization, OrganizationMember
from app.models.activity_log import ActivityLog

logger = logging.getLogger(__name__)


class AIService:
    """Service for AI-powered features and predictions"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def generate_prediction(
        self,
        organization_id: str,
        entity_type: str,
        entity_id: str,
        prediction_type: str,
        input_data: Dict[str, Any]
    ) -> Optional[AIPrediction]:
        """Generate AI prediction for an entity"""
        try:
            # Get appropriate AI model
            model = self._get_model_for_prediction(prediction_type)
            if not model:
                logger.warning(f"No model found for prediction type: {prediction_type}")
                return None
            
            # Generate prediction based on type
            if prediction_type == 'priority':
                result = await self._predict_priority(entity_type, entity_id, input_data)
            elif prediction_type == 'completion_time':
                result = await self._predict_completion_time(entity_type, entity_id, input_data)
            elif prediction_type == 'risk_level':
                result = await self._predict_risk_level(entity_type, entity_id, input_data)
            elif prediction_type == 'effort_estimate':
                result = await self._predict_effort_estimate(entity_type, entity_id, input_data)
            else:
                logger.warning(f"Unknown prediction type: {prediction_type}")
                return None
            
            # Create prediction record
            prediction = AIPrediction(
                model_id=model.id,
                organization_id=organization_id,
                entity_type=entity_type,
                entity_id=entity_id,
                prediction_type=prediction_type,
                input_data=input_data,
                prediction_result=result['prediction'],
                confidence_score=result['confidence']
            )
            
            self.db.add(prediction)
            self.db.commit()
            self.db.refresh(prediction)
            
            # Update model usage
            model.usage_count += 1
            self.db.commit()
            
            logger.info(f"Generated {prediction_type} prediction for {entity_type} {entity_id}")
            return prediction
            
        except Exception as e:
            logger.error(f"Error generating prediction: {e}")
            self.db.rollback()
            return None
    
    def _get_model_for_prediction(self, prediction_type: str) -> Optional[AIModel]:
        """Get the appropriate AI model for prediction type"""
        model = self.db.query(AIModel).filter(
            AIModel.model_type == prediction_type,
            AIModel.is_active == True,
            AIModel.is_trained == True
        ).first()
        
        if not model:
            # Create a default model if none exists
            model = self._create_default_model(prediction_type)
        
        return model
    
    def _create_default_model(self, prediction_type: str) -> AIModel:
        """Create a default AI model for prediction type"""
        model_configs = {
            'priority': {
                'name': 'Priority Prediction Model',
                'description': 'Predicts task priority based on content and context',
                'config': {'algorithm': 'ensemble', 'features': ['title', 'description', 'project', 'assignee']}
            },
            'completion_time': {
                'name': 'Completion Time Prediction Model',
                'description': 'Estimates task completion time based on historical data',
                'config': {'algorithm': 'regression', 'features': ['complexity', 'assignee_history', 'project_type']}
            },
            'risk_level': {
                'name': 'Risk Assessment Model',
                'description': 'Assesses project and task risk levels',
                'config': {'algorithm': 'classification', 'features': ['timeline', 'resources', 'dependencies']}
            },
            'effort_estimate': {
                'name': 'Effort Estimation Model',
                'description': 'Estimates effort required for tasks',
                'config': {'algorithm': 'regression', 'features': ['scope', 'complexity', 'team_experience']}
            }
        }
        
        config = model_configs.get(prediction_type, {})
        
        model = AIModel(
            model_name=config.get('name', f'{prediction_type.title()} Model'),
            model_type=prediction_type,
            model_version='1.0.0',
            description=config.get('description', f'Default {prediction_type} model'),
            configuration=config.get('config', {}),
            is_active=True,
            is_trained=True,  # Assume pre-trained for demo
            last_trained=datetime.utcnow()
        )
        
        self.db.add(model)
        self.db.commit()
        self.db.refresh(model)
        
        return model
    
    async def _predict_priority(self, entity_type: str, entity_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict priority for an entity"""
        # Simplified priority prediction logic
        # In a real implementation, this would use ML models
        
        priority_scores = {'low': 0, 'medium': 1, 'high': 2, 'urgent': 3}
        
        # Analyze input data
        score = 0
        confidence = 0.7
        
        # Check for urgency keywords
        text_content = f"{input_data.get('title', '')} {input_data.get('description', '')}".lower()
        urgency_keywords = ['urgent', 'asap', 'critical', 'emergency', 'immediate', 'deadline']
        high_priority_keywords = ['important', 'priority', 'key', 'essential', 'crucial']
        
        if any(keyword in text_content for keyword in urgency_keywords):
            score += 2
            confidence += 0.1
        elif any(keyword in text_content for keyword in high_priority_keywords):
            score += 1
            confidence += 0.05
        
        # Check due date proximity
        due_date = input_data.get('due_date')
        if due_date:
            try:
                due_datetime = datetime.fromisoformat(due_date.replace('Z', '+00:00'))
                days_until_due = (due_datetime - datetime.utcnow()).days
                
                if days_until_due <= 1:
                    score += 2
                elif days_until_due <= 3:
                    score += 1
                    
                confidence += 0.1
            except:
                pass
        
        # Check project context
        if input_data.get('project_priority') == 'high':
            score += 1
            confidence += 0.05
        
        # Determine final priority
        if score >= 3:
            predicted_priority = 'urgent'
        elif score >= 2:
            predicted_priority = 'high'
        elif score >= 1:
            predicted_priority = 'medium'
        else:
            predicted_priority = 'low'
        
        return {
            'prediction': {
                'priority': predicted_priority,
                'score': score,
                'reasoning': self._generate_priority_reasoning(score, text_content, due_date)
            },
            'confidence': min(confidence, 0.95)
        }
    
    async def _predict_completion_time(self, entity_type: str, entity_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict completion time for an entity"""
        # Simplified completion time prediction
        
        base_hours = 4  # Default estimate
        confidence = 0.6
        
        # Analyze complexity indicators
        text_content = f"{input_data.get('title', '')} {input_data.get('description', '')}".lower()
        
        # Complexity keywords
        complex_keywords = ['complex', 'difficult', 'challenging', 'research', 'analysis', 'integration']
        simple_keywords = ['simple', 'easy', 'quick', 'minor', 'small', 'fix']
        
        if any(keyword in text_content for keyword in complex_keywords):
            base_hours *= 2
            confidence += 0.1
        elif any(keyword in text_content for keyword in simple_keywords):
            base_hours *= 0.5
            confidence += 0.1
        
        # Check historical data for similar tasks
        similar_tasks = self._get_similar_completed_tasks(entity_type, input_data)
        if similar_tasks:
            avg_completion_time = sum(task.get('completion_hours', base_hours) for task in similar_tasks) / len(similar_tasks)
            base_hours = (base_hours + avg_completion_time) / 2
            confidence += 0.2
        
        # Add some randomness for realism
        estimated_hours = base_hours * (0.8 + random.random() * 0.4)
        
        return {
            'prediction': {
                'estimated_hours': round(estimated_hours, 1),
                'estimated_days': round(estimated_hours / 8, 1),
                'confidence_range': {
                    'min_hours': round(estimated_hours * 0.7, 1),
                    'max_hours': round(estimated_hours * 1.3, 1)
                }
            },
            'confidence': min(confidence, 0.9)
        }
    
    async def _predict_risk_level(self, entity_type: str, entity_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict risk level for an entity"""
        risk_score = 0
        confidence = 0.5
        risk_factors = []
        
        # Check timeline pressure
        due_date = input_data.get('due_date')
        if due_date:
            try:
                due_datetime = datetime.fromisoformat(due_date.replace('Z', '+00:00'))
                days_until_due = (due_datetime - datetime.utcnow()).days
                
                if days_until_due <= 2:
                    risk_score += 3
                    risk_factors.append('Very tight deadline')
                elif days_until_due <= 7:
                    risk_score += 2
                    risk_factors.append('Tight deadline')
                    
                confidence += 0.15
            except:
                pass
        
        # Check complexity
        text_content = f"{input_data.get('title', '')} {input_data.get('description', '')}".lower()
        risk_keywords = ['complex', 'difficult', 'uncertain', 'experimental', 'new', 'unknown']
        
        if any(keyword in text_content for keyword in risk_keywords):
            risk_score += 2
            risk_factors.append('High complexity')
            confidence += 0.1
        
        # Check dependencies
        if input_data.get('has_dependencies', False):
            risk_score += 1
            risk_factors.append('External dependencies')
            confidence += 0.1
        
        # Check team experience
        if input_data.get('team_experience', 'medium') == 'low':
            risk_score += 2
            risk_factors.append('Limited team experience')
            confidence += 0.1
        
        # Determine risk level
        if risk_score >= 5:
            risk_level = 'high'
        elif risk_score >= 3:
            risk_level = 'medium'
        elif risk_score >= 1:
            risk_level = 'low'
        else:
            risk_level = 'very_low'
        
        return {
            'prediction': {
                'risk_level': risk_level,
                'risk_score': risk_score,
                'risk_factors': risk_factors,
                'mitigation_suggestions': self._generate_risk_mitigation(risk_factors)
            },
            'confidence': min(confidence, 0.85)
        }
    
    async def _predict_effort_estimate(self, entity_type: str, entity_id: str, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """Predict effort estimate for an entity"""
        # Similar to completion time but focuses on effort points
        
        base_points = 3  # Default story points
        confidence = 0.6
        
        # Analyze scope and complexity
        text_content = f"{input_data.get('title', '')} {input_data.get('description', '')}".lower()
        
        # Effort indicators
        high_effort_keywords = ['implement', 'develop', 'create', 'build', 'design', 'research']
        low_effort_keywords = ['update', 'fix', 'adjust', 'modify', 'change', 'correct']
        
        if any(keyword in text_content for keyword in high_effort_keywords):
            base_points *= 1.5
            confidence += 0.1
        elif any(keyword in text_content for keyword in low_effort_keywords):
            base_points *= 0.7
            confidence += 0.1
        
        # Check for scope indicators
        if len(text_content) > 200:  # Longer descriptions suggest more complexity
            base_points *= 1.2
            confidence += 0.05
        
        # Fibonacci-like effort points
        effort_points = min(round(base_points), 13)  # Cap at 13 points
        
        return {
            'prediction': {
                'effort_points': effort_points,
                'effort_category': self._categorize_effort(effort_points),
                'breakdown': {
                    'analysis': round(effort_points * 0.2, 1),
                    'development': round(effort_points * 0.6, 1),
                    'testing': round(effort_points * 0.2, 1)
                }
            },
            'confidence': min(confidence, 0.8)
        }
