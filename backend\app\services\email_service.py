"""
Email service for sending notifications
"""
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional, List
import logging

from app.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """Email service for sending notifications"""
    
    def __init__(self):
        self.smtp_host = settings.smtp_host
        self.smtp_port = settings.smtp_port
        self.smtp_user = settings.smtp_user
        self.smtp_pass = settings.smtp_pass
        self.from_email = settings.from_email
        self.from_name = getattr(settings, 'from_name', 'Agno WorkSphere')
    
    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None
    ) -> bool:
        """Send an email"""
        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = to_email
            
            # Add text content
            if text_content:
                text_part = MIMEText(text_content, "plain")
                message.attach(text_part)
            
            # Add HTML content
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)
            
            # Send email
            if self.smtp_user and self.smtp_pass:
                context = ssl.create_default_context()
                with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                    server.starttls(context=context)
                    server.login(self.smtp_user, self.smtp_pass)
                    server.sendmail(self.from_email, to_email, message.as_string())
                
                logger.info(f"Email sent successfully to {to_email}")
                return True
            else:
                logger.warning("SMTP credentials not configured, email not sent")
                return False
                
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    async def send_welcome_email(
        self,
        user_email: str,
        user_name: str,
        organization_name: str,
        login_url: str = "http://localhost:3000/login"
    ) -> bool:
        """Send welcome email to new user"""
        subject = f"Welcome to {organization_name} - Your Agno WorkSphere Account is Ready!"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to Agno WorkSphere</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                .button {{ display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                .features {{ background: white; padding: 20px; border-radius: 5px; margin: 20px 0; }}
                .feature {{ margin: 10px 0; padding: 10px; border-left: 4px solid #667eea; }}
                .footer {{ text-align: center; color: #666; font-size: 12px; margin-top: 30px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 Welcome to Agno WorkSphere!</h1>
                    <p>Your project management journey starts here</p>
                </div>
                
                <div class="content">
                    <h2>Hello {user_name}!</h2>
                    
                    <p>Congratulations! You've successfully created your Agno WorkSphere account and you're now the <strong>Owner</strong> of <strong>{organization_name}</strong>.</p>
                    
                    <p>As an organization owner, you have full control over your workspace and can:</p>
                    
                    <div class="features">
                        <div class="feature">
                            <strong>👥 Manage Team Members</strong><br>
                            Invite team members and assign roles (Admin, Member, Viewer)
                        </div>
                        <div class="feature">
                            <strong>📊 Create Projects</strong><br>
                            Set up projects and organize work with Kanban boards
                        </div>
                        <div class="feature">
                            <strong>🔒 Control Access</strong><br>
                            Manage permissions and organization settings
                        </div>
                        <div class="feature">
                            <strong>📈 Track Progress</strong><br>
                            Monitor team activity and project progress
                        </div>
                    </div>
                    
                    <p>Ready to get started? Click the button below to access your dashboard:</p>
                    
                    <div style="text-align: center;">
                        <a href="{login_url}" class="button">Access Your Dashboard</a>
                    </div>
                    
                    <h3>🚀 Next Steps:</h3>
                    <ol>
                        <li><strong>Complete your profile</strong> - Add your avatar and personal information</li>
                        <li><strong>Invite your team</strong> - Add team members to your organization</li>
                        <li><strong>Create your first project</strong> - Start organizing your work</li>
                        <li><strong>Set up Kanban boards</strong> - Visualize your workflow</li>
                    </ol>
                    
                    <p>If you have any questions or need help getting started, don't hesitate to reach out to our support team.</p>
                    
                    <p>Welcome aboard!</p>
                    <p><strong>The Agno WorkSphere Team</strong></p>
                </div>
                
                <div class="footer">
                    <p>This email was sent to {user_email}</p>
                    <p>© 2024 Agno WorkSphere. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Welcome to Agno WorkSphere!
        
        Hello {user_name}!
        
        Congratulations! You've successfully created your Agno WorkSphere account and you're now the Owner of {organization_name}.
        
        As an organization owner, you have full control over your workspace and can:
        - Manage team members and assign roles
        - Create projects and organize work with Kanban boards
        - Control access and organization settings
        - Track progress and monitor team activity
        
        Ready to get started? Visit: {login_url}
        
        Next Steps:
        1. Complete your profile
        2. Invite your team
        3. Create your first project
        4. Set up Kanban boards
        
        Welcome aboard!
        The Agno WorkSphere Team
        """
        
        return await self.send_email(user_email, subject, html_content, text_content)
    
    async def send_invitation_email(
        self,
        to_email: str,
        inviter_name: str,
        organization_name: str,
        role: str,
        invitation_url: str
    ) -> bool:
        """Send invitation email to new team member"""
        subject = f"You're invited to join {organization_name} on Agno WorkSphere"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Team Invitation</title>
            <style>
                body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
                .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .content {{ background: #f9f9f9; padding: 30px; border-radius: 0 0 10px 10px; }}
                .button {{ display: inline-block; background: #667eea; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
                .role-badge {{ background: #e3f2fd; color: #1976d2; padding: 5px 15px; border-radius: 20px; font-weight: bold; }}
                .footer {{ text-align: center; color: #666; font-size: 12px; margin-top: 30px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎉 You're Invited!</h1>
                    <p>Join {organization_name} on Agno WorkSphere</p>
                </div>
                
                <div class="content">
                    <p>Hello!</p>
                    
                    <p><strong>{inviter_name}</strong> has invited you to join <strong>{organization_name}</strong> on Agno WorkSphere as a <span class="role-badge">{role.title()}</span>.</p>
                    
                    <p>Agno WorkSphere is a powerful project management platform that helps teams collaborate effectively and get things done.</p>
                    
                    <div style="text-align: center;">
                        <a href="{invitation_url}" class="button">Accept Invitation</a>
                    </div>
                    
                    <p>If you don't have an account yet, you'll be able to create one when you click the invitation link.</p>
                    
                    <p>Looking forward to having you on the team!</p>
                </div>
                
                <div class="footer">
                    <p>This invitation was sent to {to_email}</p>
                    <p>© 2024 Agno WorkSphere. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
        
        return await self.send_email(to_email, subject, html_content)


# Global email service instance
email_service = EmailService()
