import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import RoleBasedHeader from '../../components/ui/RoleBasedHeader';
import Breadcrumb from '../../components/ui/Breadcrumb';
import Button from '../../components/ui/Button';
import Icon from '../../components/AppIcon';

const ProjectOverview = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();

  // Get project data from location state or default
  const [currentProject] = useState(() => {
    const defaultProject = {
      id: 1,
      name: 'Enterprise CRM System',
      description: 'A comprehensive customer relationship management system designed to streamline sales processes and improve customer engagement across multiple channels.',
      status: 'Good',
      priority: 'High Priority',
      startDate: '1/15/2025',
      endDate: '8/30/2025',
      progress: 68,
      progressChange: '+5% this week',
      budget: {
        allocated: 250000.00,
        spent: 142000.00,
        remaining: 108000.00
      },
      team: {
        totalMembers: 4,
        activeMembers: 3,
        tasksCompleted: 34
      }
    };

    const projectFromState = location.state?.project;
    if (projectFromState) {
      // Merge with defaults to ensure all required fields exist
      return {
        ...defaultProject,
        ...projectFromState,
        budget: {
          ...defaultProject.budget,
          ...(projectFromState.budget || {})
        },
        team: {
          ...defaultProject.team,
          ...(projectFromState.team || {})
        }
      };
    }

    return defaultProject;
  });

  const [userRole, setUserRole] = useState('admin');
  const [activeTab, setActiveTab] = useState('overview');

  // Mock team members data
  const [teamMembers] = useState([
    {
      id: 1,
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'Project Manager',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      status: 'active',
      tasksAssigned: 12,
      tasksCompleted: 8
    },
    {
      id: 2,
      name: 'Michael Chen',
      email: '<EMAIL>',
      role: 'Developer',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      status: 'active',
      tasksAssigned: 15,
      tasksCompleted: 12
    },
    {
      id: 3,
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      role: 'Designer',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      status: 'active',
      tasksAssigned: 10,
      tasksCompleted: 9
    },
    {
      id: 4,
      name: 'David Kim',
      email: '<EMAIL>',
      role: 'QA Engineer',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      status: 'inactive',
      tasksAssigned: 8,
      tasksCompleted: 5
    }
  ]);

  useEffect(() => {
    // Load user data
    const loadUserData = async () => {
      try {
        // Mock user role - in real app, this would come from auth context or API
        setUserRole('admin');
      } catch (error) {
        console.error('Error loading user data:', error);
      }
    };

    loadUserData();
  }, []);

  const breadcrumbItems = [
    { label: 'Projects', path: '/projects' },
    { label: currentProject.name, path: `/project-overview/${currentProject.id}` }
  ];

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'BarChart3',
      description: 'Project summary and key metrics',
      roles: ['viewer', 'member', 'admin', 'owner']
    },
    {
      id: 'tasks',
      label: 'Tasks',
      icon: 'CheckSquare',
      description: 'Task management and tracking',
      roles: ['viewer', 'member', 'admin', 'owner']
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: 'Settings',
      description: 'Project configuration and permissions',
      roles: ['admin', 'owner']
    }
  ];

  const visibleTabs = tabs.filter(tab => tab.roles.includes(userRole));

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleGoToBoard = () => {
    navigate('/kanban-board', { 
      state: { 
        projectId: currentProject.id,
        project: currentProject 
      } 
    });
  };

  const handleAddTask = () => {
    // Navigate to kanban board with add task modal open
    navigate('/kanban-board', { 
      state: { 
        projectId: currentProject.id,
        project: currentProject,
        openAddTask: true
      } 
    });
  };

  const handleInviteMembers = () => {
    // Open invite members modal
    console.log('Opening invite members modal...');
  };

  const getStatusColor = (status) => {
    if (!status) return 'bg-muted text-text-secondary';

    switch (status.toLowerCase()) {
      case 'good':
        return 'bg-success/10 text-success';
      case 'at risk':
        return 'bg-warning/10 text-warning';
      case 'delayed':
        return 'bg-destructive/10 text-destructive';
      default:
        return 'bg-muted text-text-secondary';
    }
  };

  const getPriorityColor = (priority) => {
    if (!priority) return 'bg-muted text-text-secondary';

    switch (priority.toLowerCase()) {
      case 'high priority':
        return 'bg-destructive/10 text-destructive';
      case 'medium priority':
        return 'bg-warning/10 text-warning';
      case 'low priority':
        return 'bg-success/10 text-success';
      default:
        return 'bg-muted text-text-secondary';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <RoleBasedHeader 
        currentUser={user}
        userRole={userRole}
      />

      <div className="flex-1 flex flex-col">
        {/* Breadcrumb */}
        <div className="border-b border-border bg-card">
          <div className="px-6 py-4">
            <Breadcrumb items={breadcrumbItems} />
          </div>
        </div>

        {/* Project Header */}
        <div className="border-b border-border bg-card">
          <div className="px-6 py-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h1 className="text-2xl font-bold text-text-primary">{currentProject.name}</h1>
                  <span className={`px-2 py-1 rounded-md text-xs font-medium ${getStatusColor(currentProject.status)}`}>
                    {currentProject.status}
                  </span>
                </div>
                <p className="text-text-secondary mb-4 max-w-3xl">
                  {currentProject.description}
                </p>
                <div className="flex items-center space-x-6 text-sm">
                  <div className="flex items-center space-x-2">
                    <Icon name="Calendar" size={16} className="text-text-secondary" />
                    <span className="text-text-secondary">
                      {currentProject.startDate} - {currentProject.endDate}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Icon name="Flag" size={16} className="text-text-secondary" />
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getPriorityColor(currentProject.priority)}`}>
                      {currentProject.priority}
                    </span>
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={() => navigate('/project-management')}
                  iconName="Settings"
                  iconPosition="left"
                >
                  Settings
                </Button>
                <Button
                  variant="default"
                  onClick={handleAddTask}
                  iconName="Plus"
                  iconPosition="left"
                >
                  Add Task
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs Navigation */}
        <div className="border-b border-border bg-card">
          <div className="px-6">
            <nav className="flex space-x-8">
              {visibleTabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`flex items-center space-x-2 px-1 py-4 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-text-secondary hover:text-text-primary hover:border-border'
                  }`}
                >
                  <Icon name={tab.icon} size={16} />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="flex-1 p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Progress, Budget, Team Cards */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Progress Card */}
                <div className="bg-card border border-border rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-text-primary flex items-center">
                      <Icon name="TrendingUp" size={20} className="mr-2 text-primary" />
                      Progress
                    </h3>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-3xl font-bold text-text-primary">{currentProject.progress}%</span>
                        <span className="text-sm text-success font-medium">{currentProject.progressChange}</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${currentProject.progress}%` }}
                        ></div>
                      </div>
                      <p className="text-sm text-text-secondary mt-2">On track for completion</p>
                    </div>
                  </div>
                </div>

                {/* Budget Card */}
                <div className="bg-card border border-border rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-text-primary flex items-center">
                      <Icon name="DollarSign" size={20} className="mr-2 text-primary" />
                      Budget
                    </h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Allocated</span>
                      <span className="font-medium">${currentProject.budget.allocated.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Spent</span>
                      <span className="font-medium">${currentProject.budget.spent.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Remaining</span>
                      <span className="font-medium text-success">${currentProject.budget.remaining.toLocaleString()}</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2 mt-4">
                      <div 
                        className="bg-warning h-2 rounded-full"
                        style={{ width: `${(currentProject.budget.spent / currentProject.budget.allocated) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                </div>

                {/* Team Card */}
                <div className="bg-card border border-border rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-text-primary flex items-center">
                      <Icon name="Users" size={20} className="mr-2 text-primary" />
                      Team
                    </h3>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Total Members</span>
                      <span className="font-medium">{currentProject.team.totalMembers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Active</span>
                      <span className="font-medium">{currentProject.team.activeMembers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Tasks Completed</span>
                      <span className="font-medium">{currentProject.team.tasksCompleted}</span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleInviteMembers}
                      iconName="UserPlus"
                      iconPosition="left"
                      className="w-full mt-4"
                    >
                      Invite Members
                    </Button>
                  </div>
                </div>
              </div>

              {/* Team Members Section */}
              <div className="bg-card border border-border rounded-lg p-6">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-semibold text-text-primary">Team Members</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigate('/team-members')}
                  >
                    View All
                  </Button>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {teamMembers.map((member) => (
                    <div key={member.id} className="flex items-center space-x-3 p-3 rounded-lg border border-border">
                      <img
                        src={member.avatar}
                        alt={member.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-text-primary truncate">{member.name}</p>
                        <p className="text-xs text-text-secondary truncate">{member.role}</p>
                        <div className="flex items-center space-x-2 mt-1">
                          <span className={`w-2 h-2 rounded-full ${
                            member.status === 'active' ? 'bg-success' : 'bg-muted'
                          }`}></span>
                          <span className="text-xs text-text-secondary">
                            {member.tasksCompleted}/{member.tasksAssigned} tasks
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Quick Actions */}
              <div className="bg-card border border-border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-text-primary mb-4">Quick Actions</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Button
                    variant="outline"
                    onClick={handleGoToBoard}
                    iconName="Kanban"
                    iconPosition="left"
                    className="justify-start"
                  >
                    Go to Board
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleAddTask}
                    iconName="Plus"
                    iconPosition="left"
                    className="justify-start"
                  >
                    Add New Task
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => navigate('/team-members')}
                    iconName="Users"
                    iconPosition="left"
                    className="justify-start"
                  >
                    Manage Team
                  </Button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'tasks' && (
            <div className="text-center py-12">
              <Icon name="CheckSquare" size={48} className="mx-auto text-text-secondary mb-4" />
              <h3 className="text-lg font-semibold text-text-primary mb-2">Task Management</h3>
              <p className="text-text-secondary mb-6">Manage and track project tasks</p>
              <Button
                variant="default"
                onClick={handleGoToBoard}
                iconName="Kanban"
                iconPosition="left"
              >
                Go to Kanban Board
              </Button>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="text-center py-12">
              <Icon name="Settings" size={48} className="mx-auto text-text-secondary mb-4" />
              <h3 className="text-lg font-semibold text-text-primary mb-2">Project Settings</h3>
              <p className="text-text-secondary mb-6">Configure project settings and permissions</p>
              <Button
                variant="default"
                onClick={() => navigate('/project-management')}
                iconName="Settings"
                iconPosition="left"
              >
                Open Settings
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProjectOverview;
