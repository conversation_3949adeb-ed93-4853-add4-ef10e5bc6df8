"""
Integration service for managing third-party integrations
"""
import logging
import json
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy import func

from app.models.integrations import Integration, IntegrationSyncLog, WebhookEvent
from app.models.card import Card
from app.models.project import Project
from app.models.organization import Organization

logger = logging.getLogger(__name__)


class IntegrationService:
    """Service for managing third-party integrations"""
    
    def __init__(self, db: Session):
        self.db = db
    
    async def initialize_integration(self, integration_id: str) -> bool:
        """Initialize a new integration"""
        try:
            integration = self.db.query(Integration).filter(
                Integration.id == integration_id
            ).first()
            
            if not integration:
                logger.error(f"Integration {integration_id} not found")
                return False
            
            # Initialize based on integration type
            if integration.integration_type == 'slack':
                success = await self._initialize_slack_integration(integration)
            elif integration.integration_type == 'github':
                success = await self._initialize_github_integration(integration)
            elif integration.integration_type == 'google_calendar':
                success = await self._initialize_calendar_integration(integration)
            elif integration.integration_type == 'webhook':
                success = await self._initialize_webhook_integration(integration)
            else:
                logger.warning(f"Unknown integration type: {integration.integration_type}")
                success = False
            
            # Update integration status
            integration.status = 'active' if success else 'error'
            if not success:
                integration.error_count += 1
                integration.last_error = "Failed to initialize integration"
            
            self.db.commit()
            
            logger.info(f"Integration {integration_id} initialized: {success}")
            return success
            
        except Exception as e:
            logger.error(f"Error initializing integration {integration_id}: {e}")
            return False
    
    async def _initialize_slack_integration(self, integration: Integration) -> bool:
        """Initialize Slack integration"""
        try:
            config = integration.configuration
            bot_token = config.get('bot_token')
            
            if not bot_token:
                logger.error("Slack bot token not provided")
                return False
            
            # Test Slack API connection
            headers = {
                'Authorization': f'Bearer {bot_token}',
                'Content-Type': 'application/json'
            }
            
            response = requests.get(
                'https://slack.com/api/auth.test',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get('ok'):
                    # Store additional info from Slack
                    integration.configuration.update({
                        'team_id': data.get('team_id'),
                        'team_name': data.get('team'),
                        'bot_user_id': data.get('user_id')
                    })
                    return True
            
            logger.error(f"Slack API test failed: {response.text}")
            return False
            
        except Exception as e:
            logger.error(f"Error initializing Slack integration: {e}")
            return False
    
    async def _initialize_github_integration(self, integration: Integration) -> bool:
        """Initialize GitHub integration"""
        try:
            config = integration.configuration
            access_token = config.get('access_token')
            repository_url = config.get('repository_url')
            
            if not access_token or not repository_url:
                logger.error("GitHub access token or repository URL not provided")
                return False
            
            # Extract owner and repo from URL
            parts = repository_url.replace('https://github.com/', '').split('/')
            if len(parts) < 2:
                logger.error("Invalid GitHub repository URL")
                return False
            
            owner, repo = parts[0], parts[1]
            
            # Test GitHub API connection
            headers = {
                'Authorization': f'token {access_token}',
                'Accept': 'application/vnd.github.v3+json'
            }
            
            response = requests.get(
                f'https://api.github.com/repos/{owner}/{repo}',
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                repo_data = response.json()
                integration.configuration.update({
                    'owner': owner,
                    'repo': repo,
                    'repo_id': repo_data.get('id'),
                    'full_name': repo_data.get('full_name')
                })
                return True
            
            logger.error(f"GitHub API test failed: {response.text}")
            return False
            
        except Exception as e:
            logger.error(f"Error initializing GitHub integration: {e}")
            return False
    
    async def _initialize_calendar_integration(self, integration: Integration) -> bool:
        """Initialize calendar integration"""
        try:
            config = integration.configuration
            provider = config.get('provider')
            access_token = config.get('access_token')
            
            if not access_token:
                logger.error("Calendar access token not provided")
                return False
            
            if provider == 'google':
                # Test Google Calendar API
                headers = {'Authorization': f'Bearer {access_token}'}
                response = requests.get(
                    'https://www.googleapis.com/calendar/v3/calendars/primary',
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    calendar_data = response.json()
                    integration.configuration.update({
                        'calendar_name': calendar_data.get('summary'),
                        'calendar_timezone': calendar_data.get('timeZone')
                    })
                    return True
            
            elif provider == 'outlook':
                # Test Microsoft Graph API
                headers = {'Authorization': f'Bearer {access_token}'}
                response = requests.get(
                    'https://graph.microsoft.com/v1.0/me/calendar',
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    calendar_data = response.json()
                    integration.configuration.update({
                        'calendar_name': calendar_data.get('name'),
                        'calendar_id': calendar_data.get('id')
                    })
                    return True
            
            logger.error(f"Calendar API test failed for provider: {provider}")
            return False
            
        except Exception as e:
            logger.error(f"Error initializing calendar integration: {e}")
            return False
    
    async def _initialize_webhook_integration(self, integration: Integration) -> bool:
        """Initialize webhook integration"""
        try:
            config = integration.configuration
            webhook_url = config.get('webhook_url')
            
            if not webhook_url:
                logger.error("Webhook URL not provided")
                return False
            
            # Test webhook endpoint with a ping
            test_payload = {
                'event_type': 'ping',
                'timestamp': datetime.utcnow().isoformat(),
                'source': 'agno_worksphere'
            }
            
            headers = {'Content-Type': 'application/json'}
            if config.get('secret_token'):
                headers['X-Webhook-Secret'] = config['secret_token']
            
            response = requests.post(
                webhook_url,
                json=test_payload,
                headers=headers,
                timeout=config.get('timeout_seconds', 30)
            )
            
            if response.status_code in [200, 201, 202]:
                return True
            
            logger.error(f"Webhook test failed: {response.status_code} - {response.text}")
            return False
            
        except Exception as e:
            logger.error(f"Error initializing webhook integration: {e}")
            return False
    
    async def sync_integration(self, integration_id: str) -> bool:
        """Sync data for an integration"""
        try:
            integration = self.db.query(Integration).filter(
                Integration.id == integration_id
            ).first()
            
            if not integration or not integration.sync_enabled:
                return False
            
            # Create sync log
            sync_log = IntegrationSyncLog(
                integration_id=integration_id,
                sync_type='manual',
                status='running'
            )
            self.db.add(sync_log)
            self.db.commit()
            
            start_time = datetime.utcnow()
            
            # Perform sync based on integration type
            if integration.integration_type == 'github':
                success = await self._sync_github_data(integration, sync_log)
            elif integration.integration_type == 'google_calendar':
                success = await self._sync_calendar_data(integration, sync_log)
            else:
                logger.warning(f"Sync not implemented for: {integration.integration_type}")
                success = False
            
            # Update sync log
            end_time = datetime.utcnow()
            sync_log.status = 'success' if success else 'error'
            sync_log.completed_at = end_time
            sync_log.duration_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # Update integration
            integration.last_sync = end_time
            if success:
                integration.error_count = 0
                integration.last_error = None
            else:
                integration.error_count += 1
            
            self.db.commit()
            
            return success
            
        except Exception as e:
            logger.error(f"Error syncing integration {integration_id}: {e}")
            return False
    
    async def _sync_github_data(self, integration: Integration, sync_log: IntegrationSyncLog) -> bool:
        """Sync GitHub issues and pull requests"""
        try:
            config = integration.configuration
            access_token = config.get('access_token')
            owner = config.get('owner')
            repo = config.get('repo')
            
            headers = {
                'Authorization': f'token {access_token}',
                'Accept': 'application/vnd.github.v3+json'
            }
            
            # Sync issues if enabled
            if config.get('sync_issues', True):
                issues_url = f'https://api.github.com/repos/{owner}/{repo}/issues'
                response = requests.get(issues_url, headers=headers, timeout=30)
                
                if response.status_code == 200:
                    issues = response.json()
                    
                    for issue in issues:
                        if not issue.get('pull_request'):  # Skip pull requests
                            await self._create_task_from_github_issue(
                                integration.organization_id,
                                issue,
                                sync_log
                            )
                    
                    sync_log.records_processed += len(issues)
            
            return True
            
        except Exception as e:
            logger.error(f"Error syncing GitHub data: {e}")
            sync_log.error_details = {'error': str(e)}
            return False
    
    async def _create_task_from_github_issue(
        self, 
        organization_id: str, 
        issue: Dict[str, Any], 
        sync_log: IntegrationSyncLog
    ):
        """Create a task from GitHub issue"""
        try:
            # Check if task already exists
            existing_card = self.db.query(Card).filter(
                Card.external_id == str(issue['id']),
                Card.external_source == 'github'
            ).first()
            
            if existing_card:
                # Update existing task
                existing_card.title = issue['title']
                existing_card.description = issue.get('body', '')
                existing_card.updated_at = datetime.utcnow()
                sync_log.records_updated += 1
            else:
                # Get default project for GitHub tasks
                default_project = self.db.query(Project).filter(
                    Project.organization_id == organization_id,
                    Project.name.like('%GitHub%')
                ).first()
                
                if not default_project:
                    # Create default GitHub project
                    default_project = Project(
                        organization_id=organization_id,
                        name="GitHub Issues",
                        description="Automatically synced from GitHub",
                        status="active"
                    )
                    self.db.add(default_project)
                    self.db.flush()
                
                # Create new task
                new_card = Card(
                    project_id=default_project.id,
                    title=issue['title'],
                    description=issue.get('body', ''),
                    status='todo',
                    priority='medium',
                    external_id=str(issue['id']),
                    external_source='github',
                    external_url=issue['html_url']
                )
                self.db.add(new_card)
                sync_log.records_created += 1
            
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error creating task from GitHub issue: {e}")
            sync_log.records_failed += 1
    
    async def _sync_calendar_data(self, integration: Integration, sync_log: IntegrationSyncLog) -> bool:
        """Sync calendar events"""
        try:
            config = integration.configuration
            provider = config.get('provider')
            access_token = config.get('access_token')
            
            if provider == 'google':
                # Sync Google Calendar events
                headers = {'Authorization': f'Bearer {access_token}'}
                
                # Get events from the last 30 days
                time_min = (datetime.utcnow() - timedelta(days=30)).isoformat() + 'Z'
                time_max = (datetime.utcnow() + timedelta(days=30)).isoformat() + 'Z'
                
                params = {
                    'timeMin': time_min,
                    'timeMax': time_max,
                    'singleEvents': True,
                    'orderBy': 'startTime'
                }
                
                response = requests.get(
                    f'https://www.googleapis.com/calendar/v3/calendars/primary/events',
                    headers=headers,
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    events_data = response.json()
                    events = events_data.get('items', [])
                    
                    for event in events:
                        if config.get('create_tasks_from_events', False):
                            await self._create_task_from_calendar_event(
                                integration.organization_id,
                                event,
                                sync_log
                            )
                    
                    sync_log.records_processed += len(events)
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error syncing calendar data: {e}")
            sync_log.error_details = {'error': str(e)}
            return False
    
    async def _create_task_from_calendar_event(
        self,
        organization_id: str,
        event: Dict[str, Any],
        sync_log: IntegrationSyncLog
    ):
        """Create a task from calendar event"""
        try:
            # Skip if event already exists as task
            existing_card = self.db.query(Card).filter(
                Card.external_id == event['id'],
                Card.external_source == 'google_calendar'
            ).first()
            
            if existing_card:
                sync_log.records_updated += 1
                return
            
            # Get default project for calendar tasks
            default_project = self.db.query(Project).filter(
                Project.organization_id == organization_id,
                Project.name.like('%Calendar%')
            ).first()
            
            if not default_project:
                default_project = Project(
                    organization_id=organization_id,
                    name="Calendar Events",
                    description="Automatically synced from calendar",
                    status="active"
                )
                self.db.add(default_project)
                self.db.flush()
            
            # Parse event date
            start_time = event.get('start', {})
            due_date = None
            if start_time.get('dateTime'):
                due_date = datetime.fromisoformat(
                    start_time['dateTime'].replace('Z', '+00:00')
                )
            
            # Create task
            new_card = Card(
                project_id=default_project.id,
                title=event.get('summary', 'Calendar Event'),
                description=event.get('description', ''),
                status='todo',
                priority='medium',
                due_date=due_date,
                external_id=event['id'],
                external_source='google_calendar',
                external_url=event.get('htmlLink')
            )
            
            self.db.add(new_card)
            sync_log.records_created += 1
            self.db.commit()
            
        except Exception as e:
            logger.error(f"Error creating task from calendar event: {e}")
            sync_log.records_failed += 1
    
    async def process_webhook(self, webhook_event_id: str) -> bool:
        """Process incoming webhook event"""
        try:
            webhook_event = self.db.query(WebhookEvent).filter(
                WebhookEvent.id == webhook_event_id
            ).first()
            
            if not webhook_event:
                return False
            
            webhook_event.processing_attempts += 1
            webhook_event.last_attempt = datetime.utcnow()
            
            # Process based on event source
            payload = webhook_event.payload
            
            if webhook_event.event_source == 'github':
                success = await self._process_github_webhook(webhook_event, payload)
            elif webhook_event.event_source == 'slack':
                success = await self._process_slack_webhook(webhook_event, payload)
            else:
                # Generic webhook processing
                success = await self._process_generic_webhook(webhook_event, payload)
            
            webhook_event.status = 'processed' if success else 'failed'
            if success:
                webhook_event.processed_at = datetime.utcnow()
            
            self.db.commit()
            
            return success
            
        except Exception as e:
            logger.error(f"Error processing webhook {webhook_event_id}: {e}")
            return False
    
    async def _process_github_webhook(self, webhook_event: WebhookEvent, payload: Dict[str, Any]) -> bool:
        """Process GitHub webhook event"""
        try:
            event_type = payload.get('action')
            
            if 'issue' in payload:
                issue = payload['issue']
                
                if event_type == 'opened':
                    # Create new task from issue
                    await self._create_task_from_github_issue(
                        webhook_event.organization_id,
                        issue,
                        None  # No sync log for webhook events
                    )
                elif event_type in ['edited', 'reopened']:
                    # Update existing task
                    existing_card = self.db.query(Card).filter(
                        Card.external_id == str(issue['id']),
                        Card.external_source == 'github'
                    ).first()
                    
                    if existing_card:
                        existing_card.title = issue['title']
                        existing_card.description = issue.get('body', '')
                        if event_type == 'reopened':
                            existing_card.status = 'todo'
                        self.db.commit()
                
                elif event_type == 'closed':
                    # Mark task as completed
                    existing_card = self.db.query(Card).filter(
                        Card.external_id == str(issue['id']),
                        Card.external_source == 'github'
                    ).first()
                    
                    if existing_card:
                        existing_card.status = 'completed'
                        self.db.commit()
            
            return True
            
        except Exception as e:
            logger.error(f"Error processing GitHub webhook: {e}")
            webhook_event.error_message = str(e)
            return False
    
    async def _process_slack_webhook(self, webhook_event: WebhookEvent, payload: Dict[str, Any]) -> bool:
        """Process Slack webhook event"""
        try:
            # Handle Slack events like mentions, messages, etc.
            event_type = payload.get('type')
            
            if event_type == 'url_verification':
                # Slack URL verification
                webhook_event.response_data = {'challenge': payload.get('challenge')}
                return True
            
            # Process other Slack events as needed
            return True
            
        except Exception as e:
            logger.error(f"Error processing Slack webhook: {e}")
            webhook_event.error_message = str(e)
            return False
    
    async def _process_generic_webhook(self, webhook_event: WebhookEvent, payload: Dict[str, Any]) -> bool:
        """Process generic webhook event"""
        try:
            # Basic webhook processing - just log the event
            webhook_event.response_data = {'status': 'received', 'processed': True}
            return True
            
        except Exception as e:
            logger.error(f"Error processing generic webhook: {e}")
            webhook_event.error_message = str(e)
            return False
