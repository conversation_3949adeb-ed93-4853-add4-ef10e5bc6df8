{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\ui\\\\RoleBasedHeader.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport Icon from '../AppIcon';\nimport Button from './Button';\nimport ProfessionalButton, { OwnerActionButton, IconButton } from './ProfessionalButton';\nimport { Flex } from './ProfessionalLayout';\nimport authService from '../../utils/authService';\nimport CreateProjectModal from '../modals/CreateProjectModal';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RoleBasedHeader = ({\n  userRole = 'member',\n  currentUser,\n  currentOrganization\n}) => {\n  _s();\n  const [isOrgDropdownOpen, setIsOrgDropdownOpen] = useState(false);\n  const [isProjectDropdownOpen, setIsProjectDropdownOpen] = useState(false);\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] = useState(false);\n  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState(false);\n  const location = useLocation();\n  const navigate = useNavigate();\n  const orgDropdownRef = useRef(null);\n  const projectDropdownRef = useRef(null);\n  const userDropdownRef = useRef(null);\n  const mobileMenuRef = useRef(null);\n  const notificationDropdownRef = useRef(null);\n\n  // Default user data if not provided\n  const user = {\n    name: 'Sarah Johnson',\n    email: '<EMAIL>',\n    avatar: '/assets/images/avatar.jpg',\n    role: 'Project Manager',\n    ...currentUser\n  };\n\n  // Default organization data if not provided\n  const organization = currentOrganization || {\n    name: 'Acme Corporation',\n    domain: 'acme.com',\n    logo: '/assets/images/org-logo.png'\n  };\n  const availableOrganizations = [{\n    id: 1,\n    name: 'Acme Corporation',\n    domain: 'acme.com',\n    role: 'Admin'\n  }, {\n    id: 2,\n    name: 'TechStart Inc',\n    domain: 'techstart.com',\n    role: 'Member'\n  }, {\n    id: 3,\n    name: 'Global Solutions',\n    domain: 'globalsol.com',\n    role: 'Manager'\n  }];\n\n  // Project state and data\n  const [currentProject, setCurrentProject] = useState({\n    id: 1,\n    name: 'Website Redesign',\n    description: 'Complete redesign of company website',\n    status: 'active'\n  });\n\n  // Mock project data based on user role\n  const getAvailableProjects = role => {\n    const allProjects = [{\n      id: 1,\n      name: 'Website Redesign',\n      description: 'Complete redesign of company website',\n      status: 'active',\n      memberRole: 'assigned'\n    }, {\n      id: 2,\n      name: 'Mobile App Development',\n      description: 'New mobile application for customers',\n      status: 'active',\n      memberRole: 'assigned'\n    }, {\n      id: 3,\n      name: 'Marketing Campaign',\n      description: 'Q4 marketing campaign planning',\n      status: 'active',\n      memberRole: 'not-assigned'\n    }, {\n      id: 4,\n      name: 'Database Migration',\n      description: 'Migrate to new database infrastructure',\n      status: 'active',\n      memberRole: 'not-assigned'\n    }, {\n      id: 5,\n      name: 'Security Audit',\n      description: 'Comprehensive security review',\n      status: 'planning',\n      memberRole: 'not-assigned'\n    }];\n\n    // Members only see projects they're assigned to\n    if (role === 'member') {\n      return allProjects.filter(project => project.memberRole === 'assigned');\n    }\n\n    // Admins and Owners see all projects\n    return allProjects;\n  };\n  const availableProjects = getAvailableProjects(userRole);\n\n  // Role-based navigation configuration\n  const getNavigationItems = role => {\n    const baseItems = [{\n      label: 'Projects',\n      path: '/kanban-board',\n      icon: 'Kanban',\n      roles: ['viewer', 'member', 'admin', 'owner']\n    }, {\n      label: 'Team Members',\n      path: '/team-members',\n      icon: 'Users',\n      roles: ['member', 'admin', 'owner']\n    }];\n    const adminItems = [\n      // Organization settings moved to owner-only items\n    ];\n    const ownerItems = [{\n      label: 'Organization',\n      path: '/organization-settings',\n      icon: 'Settings',\n      roles: ['owner']\n    }, {\n      label: 'Analytics',\n      path: '/analytics',\n      icon: 'BarChart3',\n      roles: ['owner']\n    }, {\n      label: 'Billing',\n      path: '/billing',\n      icon: 'CreditCard',\n      roles: ['owner']\n    }];\n\n    // Filter items based on user role\n    const allItems = [...baseItems, ...adminItems, ...ownerItems];\n    return allItems.filter(item => item.roles.includes(role.toLowerCase()));\n  };\n\n  // Get role-specific features\n  const getRoleFeatures = role => {\n    const features = {\n      viewer: {\n        canCreateProjects: false,\n        canInviteMembers: false,\n        canManageSettings: false,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: false\n      },\n      member: {\n        canCreateProjects: true,\n        canInviteMembers: false,\n        canManageSettings: false,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: false\n      },\n      admin: {\n        canCreateProjects: true,\n        canInviteMembers: true,\n        canManageSettings: true,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: true\n      },\n      owner: {\n        canCreateProjects: true,\n        canInviteMembers: true,\n        canManageSettings: true,\n        canViewAnalytics: true,\n        canManageBilling: true,\n        canSwitchOrganizations: true\n      }\n    };\n    return features[role.toLowerCase()] || features.viewer;\n  };\n  const navigationItems = getNavigationItems(userRole);\n  const roleFeatures = getRoleFeatures(userRole);\n\n  // Enhanced notification data with comprehensive structure\n  const [notifications, setNotifications] = useState([{\n    id: 'task_001',\n    type: 'task_assignment',\n    title: 'New Task Assigned',\n    message: 'You have been assigned to work on the payment gateway integration for the e-commerce platform.',\n    timestamp: new Date(Date.now() - 30 * 60 * 1000),\n    // 30 minutes ago\n    isRead: false,\n    priority: 'high',\n    data: {\n      taskId: 'task_123',\n      taskTitle: 'Payment Gateway Integration',\n      taskDescription: 'Implement secure payment processing with Stripe API for the e-commerce platform. This includes setting up webhooks, handling payment failures, and ensuring PCI compliance.',\n      projectName: 'E-commerce Platform',\n      projectId: 'proj_001',\n      assignerName: 'Emily Davis',\n      assignerAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n      // 7 days from now\n      estimatedHours: 16\n    },\n    actions: [{\n      label: 'Accept Task',\n      action: 'accept_task',\n      variant: 'primary'\n    }, {\n      label: 'View Details',\n      action: 'view_task',\n      variant: 'secondary'\n    }, {\n      label: 'Decline',\n      action: 'decline_task',\n      variant: 'danger'\n    }]\n  }, {\n    id: 'meeting_001',\n    type: 'meeting_invite',\n    title: 'Team Sprint Planning',\n    message: 'You are invited to the sprint planning meeting for the upcoming development cycle.',\n    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    // 2 hours ago\n    isRead: false,\n    priority: 'medium',\n    data: {\n      meetingId: 'meet_456',\n      meetingTitle: 'Sprint Planning - Q1 2025',\n      organizer: 'Sarah Johnson',\n      organizerAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n      startTime: new Date(Date.now() + 24 * 60 * 60 * 1000),\n      // Tomorrow\n      duration: 120,\n      // minutes\n      location: 'Conference Room A / Zoom',\n      attendees: ['John Doe', 'Alice Smith', 'Bob Wilson'],\n      agenda: 'Review previous sprint, plan upcoming tasks, estimate story points'\n    },\n    actions: [{\n      label: 'Accept',\n      action: 'accept_meeting',\n      variant: 'primary'\n    }, {\n      label: 'Decline',\n      action: 'decline_meeting',\n      variant: 'secondary'\n    }, {\n      label: 'View Details',\n      action: 'view_meeting',\n      variant: 'secondary'\n    }]\n  }, {\n    id: 'ai_001',\n    type: 'ai_suggestion',\n    title: 'AI Recommendation: Task Prioritization',\n    message: 'Based on your current workload and deadlines, I recommend prioritizing the API integration task.',\n    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    // 4 hours ago\n    isRead: true,\n    priority: 'low',\n    data: {\n      suggestionType: 'task_prioritization',\n      confidence: 0.85,\n      reasoning: 'The API integration task is blocking 3 other team members and has a deadline in 2 days.',\n      recommendedActions: ['Move API integration to top priority', 'Allocate 6 hours today for this task', 'Consider requesting help from backend team'],\n      impactAnalysis: {\n        timesSaved: '4 hours',\n        teamEfficiency: '+15%',\n        riskReduction: 'High'\n      }\n    },\n    actions: [{\n      label: 'Apply Suggestion',\n      action: 'apply_ai_suggestion',\n      variant: 'primary'\n    }, {\n      label: 'Learn More',\n      action: 'view_ai_details',\n      variant: 'secondary'\n    }]\n  }, {\n    id: 'deadline_001',\n    type: 'project_update',\n    title: 'Deadline Approaching',\n    message: 'The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.',\n    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n    // 6 hours ago\n    isRead: true,\n    priority: 'high',\n    data: {\n      projectId: 'proj_002',\n      projectName: 'Mobile App Redesign',\n      deadlineDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),\n      // 2 days from now\n      completionPercentage: 75,\n      remainingTasks: 3,\n      assignedTo: 'Design Team'\n    },\n    actions: [{\n      label: 'View Project',\n      action: 'view_project',\n      variant: 'primary'\n    }, {\n      label: 'Update Status',\n      action: 'update_status',\n      variant: 'secondary'\n    }]\n  }]);\n\n  // Notification state and filtering\n  const [notificationFilter, setNotificationFilter] = useState('all'); // 'all', 'unread', 'high_priority'\n  const [isLoading, setIsLoading] = useState(false);\n  const unreadCount = notifications.filter(n => !n.isRead).length;\n  const highPriorityCount = notifications.filter(n => n.priority === 'high' && !n.isRead).length;\n\n  // Filter notifications based on current filter\n  const filteredNotifications = notifications.filter(notification => {\n    switch (notificationFilter) {\n      case 'unread':\n        return !notification.isRead;\n      case 'high_priority':\n        return notification.priority === 'high';\n      default:\n        return true;\n    }\n  });\n  const markAsRead = notificationId => {\n    setNotifications(prev => prev.map(notification => notification.id === notificationId ? {\n      ...notification,\n      isRead: true\n    } : notification));\n  };\n  const markAllAsRead = () => {\n    setNotifications(prev => prev.map(notification => ({\n      ...notification,\n      isRead: true\n    })));\n  };\n  const getNotificationIcon = type => {\n    switch (type) {\n      case 'task_assignment':\n        return 'UserPlus';\n      case 'meeting_invite':\n        return 'Calendar';\n      case 'meeting_reminder':\n        return 'Clock';\n      case 'ai_suggestion':\n        return 'Zap';\n      case 'project_update':\n        return 'AlertTriangle';\n      default:\n        return 'Bell';\n    }\n  };\n  const getPriorityColor = priority => {\n    switch (priority) {\n      case 'high':\n        return 'text-destructive';\n      case 'medium':\n        return 'text-warning';\n      case 'low':\n        return 'text-muted-foreground';\n      default:\n        return 'text-muted-foreground';\n    }\n  };\n  const formatTimestamp = timestamp => {\n    const now = new Date();\n    const diff = now - timestamp;\n    const minutes = Math.floor(diff / (1000 * 60));\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n    if (minutes < 1) return 'Just now';\n    if (minutes < 60) return `${minutes}m ago`;\n    if (hours < 24) return `${hours}h ago`;\n    if (days < 7) return `${days}d ago`;\n    return timestamp.toLocaleDateString();\n  };\n\n  // Role-based notification filtering\n  const getRoleBasedNotifications = (notifications, userRole) => {\n    return notifications.filter(notification => {\n      switch (notification.type) {\n        case 'meeting_invite':\n          // All roles can receive meeting invites\n          return true;\n        case 'ai_suggestion':\n          // AI suggestions available to all roles\n          return true;\n        case 'task_assignment':\n          // Task assignments for members and above only\n          // Viewers cannot receive task assignments in this system\n          return ['member', 'admin', 'owner'].includes(userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase());\n        case 'project_update':\n          // Project updates for all roles\n          return true;\n        default:\n          return true;\n      }\n    });\n  };\n\n  // Notification action handlers\n  const handleNotificationAction = async (notificationId, action, notificationData) => {\n    setIsLoading(true);\n    try {\n      switch (action) {\n        case 'accept_task':\n          // Accept task assignment\n          console.log('Accepting task:', notificationData.taskId);\n          // Navigate to kanban board with task highlighted\n          navigate('/kanban-board', {\n            state: {\n              projectId: notificationData.projectId,\n              highlightTaskId: notificationData.taskId,\n              taskAccepted: true\n            }\n          });\n          // Remove notification after acceptance\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n        case 'view_task':\n          // View task details\n          navigate('/kanban-board', {\n            state: {\n              projectId: notificationData.projectId,\n              highlightTaskId: notificationData.taskId\n            }\n          });\n          markAsRead(notificationId);\n          break;\n        case 'decline_task':\n          // Decline task assignment\n          const reason = prompt('Please provide a reason for declining this task (optional):');\n          console.log('Declining task:', notificationData.taskId, 'Reason:', reason);\n          // Remove notification after declining\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n        case 'accept_meeting':\n          // Accept meeting invitation\n          console.log('Accepting meeting:', notificationData.meetingId);\n          // Update meeting status and remove notification\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n        case 'decline_meeting':\n          // Decline meeting invitation\n          console.log('Declining meeting:', notificationData.meetingId);\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n        case 'view_meeting':\n          // View meeting details\n          console.log('Viewing meeting details:', notificationData.meetingId);\n          markAsRead(notificationId);\n          break;\n        case 'join_meeting':\n          // Join meeting (open meeting link)\n          console.log('Joining meeting:', notificationData.meetingId);\n          window.open('https://zoom.us/j/meeting-room', '_blank');\n          markAsRead(notificationId);\n          break;\n        case 'apply_ai_suggestion':\n          // Apply AI suggestion\n          console.log('Applying AI suggestion:', notificationData.suggestionType);\n          markAsRead(notificationId);\n          break;\n        case 'view_project':\n          // View project details\n          navigate('/project-overview', {\n            state: {\n              projectId: notificationData.projectId\n            }\n          });\n          markAsRead(notificationId);\n          break;\n        default:\n          console.log('Unknown action:', action);\n          markAsRead(notificationId);\n      }\n    } catch (error) {\n      console.error('Error handling notification action:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const isActivePath = path => location.pathname === path;\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (orgDropdownRef.current && !orgDropdownRef.current.contains(event.target)) {\n        setIsOrgDropdownOpen(false);\n      }\n      if (projectDropdownRef.current && !projectDropdownRef.current.contains(event.target)) {\n        setIsProjectDropdownOpen(false);\n      }\n      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {\n        setIsUserDropdownOpen(false);\n      }\n      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {\n        setIsMobileMenuOpen(false);\n      }\n      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target)) {\n        setIsNotificationDropdownOpen(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n  const handleOrganizationSwitch = orgId => {\n    console.log('Switching to organization:', orgId);\n    setIsOrgDropdownOpen(false);\n  };\n  const handleProjectSwitch = project => {\n    console.log('Switching to project:', project);\n    setCurrentProject(project);\n    setIsProjectDropdownOpen(false);\n    // Navigate to the project overview page\n    navigate('/project-overview', {\n      state: {\n        project: project\n      }\n    });\n  };\n  const handleCreateProject = () => {\n    console.log('Creating new project...');\n    setIsProjectDropdownOpen(false);\n    setIsCreateProjectModalOpen(true);\n  };\n  const handleCreateProjectSubmit = async projectData => {\n    try {\n      console.log('Creating project:', projectData);\n      // Here you would typically call an API to create the project\n      // For now, we'll just add it to the available projects\n      const newProject = {\n        id: Date.now(),\n        name: projectData.name,\n        description: projectData.description,\n        status: 'active',\n        memberRole: 'assigned'\n      };\n\n      // Update current project to the newly created one\n      setCurrentProject(newProject);\n      setIsCreateProjectModalOpen(false);\n\n      // Navigate to the new project's overview page\n      navigate('/project-overview', {\n        state: {\n          project: newProject\n        }\n      });\n    } catch (error) {\n      console.error('Failed to create project:', error);\n      throw error;\n    }\n  };\n  const handleLogout = async () => {\n    try {\n      console.log('Logging out...');\n\n      // Clear authentication state\n      await authService.logout();\n\n      // Close any open dropdowns\n      setIsUserDropdownOpen(false);\n      setIsOrgDropdownOpen(false);\n      setIsProjectDropdownOpen(false);\n      setIsMobileMenuOpen(false);\n\n      // Redirect to login page\n      navigate('/login', {\n        replace: true\n      });\n    } catch (error) {\n      console.error('Error during logout:', error);\n      // Even if logout fails, redirect to login page\n      navigate('/login', {\n        replace: true\n      });\n    }\n  };\n\n  // Don't render header on auth pages\n  if (location.pathname === '/login' || location.pathname === '/register') {\n    return null;\n  }\n\n  // Get role badge color\n  const getRoleBadgeColor = role => {\n    const colors = {\n      viewer: 'bg-gray-100 text-gray-800',\n      member: 'bg-blue-100 text-blue-800',\n      admin: 'bg-purple-100 text-purple-800',\n      owner: 'bg-green-100 text-green-800'\n    };\n    return colors[role.toLowerCase()] || colors.viewer;\n  };\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: `\n      fixed top-0 left-0 right-0 z-50 transition-all duration-300\n      ${userRole === 'owner' ? 'bg-gradient-to-r from-white via-purple-50/40 to-white border-b border-purple-200/60 shadow-xl backdrop-blur-sm' : 'bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-lg'}\n    `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between h-16 px-4 lg:px-6 max-w-7xl mx-auto\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/kanban-board\",\n          className: \"flex items-center space-x-3 group transition-all duration-200 hover:scale-105\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `\n              w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-200\n              ${userRole === 'owner' ? 'bg-gradient-to-br from-purple-600 to-pink-600 shadow-lg group-hover:shadow-xl' : 'bg-gradient-to-br from-blue-600 to-indigo-600 shadow-md group-hover:shadow-lg'}\n            `,\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              viewBox: \"0 0 24 24\",\n              className: \"w-6 h-6 text-white\",\n              fill: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 556,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: `\n                text-xl font-bold transition-colors duration-200\n                ${userRole === 'owner' ? 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent' : 'text-gray-900'}\n              `,\n              children: \"Agno WorkSphere\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 561,\n              columnNumber: 15\n            }, this), userRole === 'owner' && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-medium text-purple-600 -mt-1\",\n              children: \"Owner Console\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 571,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 547,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden lg:flex items-center space-x-6\",\n        children: [roleFeatures.canSwitchOrganizations ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: orgDropdownRef,\n          children: [/*#__PURE__*/_jsxDEV(ProfessionalButton, {\n            variant: userRole === 'owner' ? 'owner' : 'ghost',\n            onClick: () => setIsOrgDropdownOpen(!isOrgDropdownOpen),\n            className: \"flex items-center space-x-3 px-4 py-2.5 transition-all duration-200\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `\n                  w-7 h-7 rounded-lg flex items-center justify-center font-semibold text-sm\n                  ${userRole === 'owner' ? 'bg-gradient-to-br from-purple-100 to-pink-100 text-purple-700' : 'bg-gray-100 text-gray-700'}\n                `,\n              children: organization.name.charAt(0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 588,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-sm\",\n                children: organization.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 19\n              }, this), userRole === 'owner' && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-purple-600\",\n                children: \"Organization Owner\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 600,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 597,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"ChevronDown\",\n              size: 16,\n              className: `\n                  transition-transform duration-200\n                  ${isOrgDropdownOpen ? 'rotate-180' : ''}\n                  ${userRole === 'owner' ? 'text-purple-600' : 'text-gray-500'}\n                `\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 583,\n            columnNumber: 15\n          }, this), isOrgDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `\n                  absolute top-full left-0 mt-2 w-72 rounded-xl shadow-2xl z-50\n                  animate-scale-in border backdrop-blur-sm\n                  ${userRole === 'owner' ? 'bg-gradient-to-br from-white to-purple-50/50 border-purple-200' : 'bg-white border-gray-200'}\n                `,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `\n                      text-xs font-semibold uppercase tracking-wider px-3 py-2 rounded-lg\n                      ${userRole === 'owner' ? 'text-purple-700 bg-purple-100/50' : 'text-gray-600 bg-gray-100'}\n                    `,\n                children: \"Switch Organization\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 620,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 space-y-1\",\n                children: availableOrganizations.map(org => /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleOrganizationSwitch(org.id),\n                  className: `\n                            w-full flex items-center space-x-3 px-3 py-3 rounded-lg\n                            transition-all duration-200 text-left group\n                            ${userRole === 'owner' ? 'hover:bg-purple-100/50 hover:shadow-md' : 'hover:bg-gray-50 hover:shadow-sm'}\n                          `,\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `\n                            w-10 h-10 rounded-lg flex items-center justify-center font-semibold text-sm\n                            ${userRole === 'owner' ? 'bg-gradient-to-br from-purple-100 to-pink-100 text-purple-700' : 'bg-gray-100 text-gray-700'}\n                          `,\n                    children: org.name.charAt(0)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 643,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm font-semibold text-gray-900\",\n                      children: org.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 653,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500\",\n                      children: org.domain\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 654,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 652,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `\n                              text-xs px-2 py-1 rounded-full font-medium\n                              ${org.role === 'owner' ? 'bg-purple-100 text-purple-700' : org.role === 'admin' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}\n                            `,\n                      children: org.role\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 657,\n                      columnNumber: 29\n                    }, this), org.id === 1 && /*#__PURE__*/_jsxDEV(Icon, {\n                      name: \"Check\",\n                      size: 16,\n                      className: \"text-green-600\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 669,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 656,\n                    columnNumber: 27\n                  }, this)]\n                }, org.id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 631,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 629,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 611,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 13\n        }, this) :\n        /*#__PURE__*/\n        // For members and viewers - show organization name without dropdown\n        _jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 px-3 py-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-6 h-6 bg-muted rounded-sm flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-medium text-text-primary\",\n              children: organization.name.charAt(0)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 683,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 682,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium text-text-primary\",\n            children: organization.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 687,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 681,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"flex items-center space-x-1\",\n          children: navigationItems.map(item => {\n            // Special handling for Projects item - make it a dropdown\n            if (item.label === 'Projects') {\n              return /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative\",\n                ref: projectDropdownRef,\n                children: [/*#__PURE__*/_jsxDEV(ProfessionalButton, {\n                  variant: userRole === 'owner' ? 'outline' : 'ghost',\n                  onClick: () => setIsProjectDropdownOpen(!isProjectDropdownOpen),\n                  className: `\n                        flex items-center space-x-2 px-4 py-2.5 rounded-lg text-sm font-medium\n                        transition-all duration-200 group\n                        ${isActivePath(item.path) ? userRole === 'owner' ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg' : 'bg-blue-600 text-white shadow-md' : userRole === 'owner' ? 'text-purple-700 hover:bg-purple-50 border-purple-200' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'}\n                      `,\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: item.icon,\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 714,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: item.label\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 715,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"ChevronDown\",\n                    size: 12,\n                    className: `\n                          ml-1 transition-transform duration-200\n                          ${isProjectDropdownOpen ? 'rotate-180' : ''}\n                        `\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 716,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 698,\n                  columnNumber: 21\n                }, this), isProjectDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `\n                        absolute top-full left-0 mt-2 w-80 rounded-xl shadow-2xl z-50\n                        animate-scale-in border backdrop-blur-sm\n                        ${userRole === 'owner' ? 'bg-gradient-to-br from-white to-purple-50/50 border-purple-200' : 'bg-white border-gray-200'}\n                      `,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"p-4\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: `\n                            text-xs font-semibold uppercase tracking-wider px-3 py-2 rounded-lg mb-3\n                            ${userRole === 'owner' ? 'text-purple-700 bg-purple-100/50' : 'text-gray-600 bg-gray-100'}\n                          `,\n                      children: \"Switch Project\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 736,\n                      columnNumber: 27\n                    }, this), (userRole === 'admin' || userRole === 'owner') && /*#__PURE__*/_jsxDEV(_Fragment, {\n                      children: /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: handleCreateProject,\n                        className: `\n                                  w-full flex items-center space-x-3 px-3 py-3 rounded-lg\n                                  transition-all duration-200 text-left group mb-3\n                                  border-b pb-3\n                                  ${userRole === 'owner' ? 'hover:bg-purple-100/50 hover:shadow-md border-purple-200' : 'hover:bg-gray-50 hover:shadow-sm border-gray-200'}\n                                `,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `\n                                  w-10 h-10 rounded-lg flex items-center justify-center\n                                  ${userRole === 'owner' ? 'bg-gradient-to-br from-purple-500 to-pink-500 text-white' : 'bg-gradient-to-br from-blue-500 to-indigo-500 text-white'}\n                                  group-hover:scale-110 transition-transform duration-200\n                                `,\n                          children: /*#__PURE__*/_jsxDEV(Icon, {\n                            name: \"Plus\",\n                            size: 18\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 769,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 761,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: `\n                                    text-sm font-semibold\n                                    ${userRole === 'owner' ? 'text-purple-700' : 'text-blue-700'}\n                                  `,\n                            children: \"Create New Project\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 772,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Start a new project with AI setup\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 778,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 771,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(Icon, {\n                          name: \"ArrowRight\",\n                          size: 16,\n                          className: `\n                                    transition-transform duration-200 group-hover:translate-x-1\n                                    ${userRole === 'owner' ? 'text-purple-600' : 'text-blue-600'}\n                                  `\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 780,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 749,\n                        columnNumber: 31\n                      }, this)\n                    }, void 0, false), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"space-y-1\",\n                      children: availableProjects.map(project => /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => handleProjectSwitch(project),\n                        className: `\n                                  w-full flex items-center space-x-3 px-3 py-3 rounded-lg\n                                  transition-all duration-200 text-left group\n                                  ${userRole === 'owner' ? 'hover:bg-purple-100/50 hover:shadow-md' : 'hover:bg-gray-50 hover:shadow-sm'}\n                                `,\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: `\n                                  w-10 h-10 rounded-lg flex items-center justify-center\n                                  ${userRole === 'owner' ? 'bg-gradient-to-br from-purple-100 to-pink-100 text-purple-600' : 'bg-gray-100 text-gray-600'}\n                                `,\n                          children: /*#__PURE__*/_jsxDEV(Icon, {\n                            name: \"Folder\",\n                            size: 18\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 814,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 807,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-sm font-semibold text-gray-900\",\n                            children: project.name\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 817,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"text-xs text-gray-500 truncate\",\n                            children: project.description\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 818,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 816,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex flex-col items-end space-y-1\",\n                          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `\n                                    text-xs px-2 py-1 rounded-full font-medium\n                                    ${project.status === 'active' ? 'bg-green-100 text-green-700' : project.status === 'planning' ? 'bg-yellow-100 text-yellow-700' : 'bg-gray-100 text-gray-700'}\n                                  `,\n                            children: project.status\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 821,\n                            columnNumber: 35\n                          }, this), userRole === 'member' && /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: \"text-xs text-gray-500\",\n                            children: \"Assigned\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 833,\n                            columnNumber: 37\n                          }, this), project.id === currentProject.id && /*#__PURE__*/_jsxDEV(Icon, {\n                            name: \"Check\",\n                            size: 16,\n                            className: \"text-green-600\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 836,\n                            columnNumber: 37\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 820,\n                          columnNumber: 33\n                        }, this)]\n                      }, project.id, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 795,\n                        columnNumber: 31\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 793,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 735,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 727,\n                  columnNumber: 23\n                }, this)]\n              }, item.path, true, {\n                fileName: _jsxFileName,\n                lineNumber: 697,\n                columnNumber: 19\n              }, this);\n            }\n\n            // Regular navigation items\n            return /*#__PURE__*/_jsxDEV(Link, {\n              to: item.path,\n              className: `\n                    flex items-center space-x-2 px-4 py-2.5 rounded-lg text-sm font-medium\n                    transition-all duration-200 group\n                    ${isActivePath(item.path) ? userRole === 'owner' ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg' : 'bg-blue-600 text-white shadow-md' : userRole === 'owner' ? 'text-purple-700 hover:bg-purple-50 hover:text-purple-800' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'}\n                  `,\n              children: [/*#__PURE__*/_jsxDEV(Icon, {\n                name: item.icon,\n                size: 16,\n                className: \"group-hover:scale-110 transition-transform duration-200\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 867,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: item.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 19\n              }, this)]\n            }, item.path, true, {\n              fileName: _jsxFileName,\n              lineNumber: 851,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 692,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: notificationDropdownRef,\n          children: [/*#__PURE__*/_jsxDEV(ProfessionalButton, {\n            variant: \"ghost\",\n            onClick: () => setIsNotificationDropdownOpen(!isNotificationDropdownOpen),\n            className: `\n                relative p-2.5 rounded-lg transition-all duration-200 group\n                ${userRole === 'owner' ? 'hover:bg-purple-50 text-purple-600' : 'hover:bg-gray-50 text-gray-600'}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Bell\",\n              size: 20,\n              className: \"group-hover:scale-110 transition-transform duration-200\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 15\n            }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: `\n                  absolute -top-1 -right-1 h-5 w-5 text-xs font-bold rounded-full\n                  flex items-center justify-center animate-pulse\n                  ${userRole === 'owner' ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white' : 'bg-red-500 text-white'}\n                `,\n              children: unreadCount > 9 ? '9+' : unreadCount\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 900,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 883,\n            columnNumber: 13\n          }, this), isNotificationDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-full right-0 mt-1 w-96 bg-popover border border-border rounded-md shadow-elevated z-1010 md:w-80\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-border\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"font-semibold text-text-primary flex items-center\",\n                  children: [\"Notifications\", unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-2 bg-destructive text-destructive-foreground text-xs px-2 py-1 rounded-full\",\n                    children: unreadCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 921,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 918,\n                  columnNumber: 21\n                }, this), unreadCount > 0 && /*#__PURE__*/_jsxDEV(Button, {\n                  variant: \"ghost\",\n                  size: \"sm\",\n                  onClick: markAllAsRead,\n                  className: \"text-xs text-primary hover:text-primary\",\n                  children: \"Mark all read\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 927,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 917,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setNotificationFilter('all'),\n                  className: `px-3 py-1 text-xs rounded-full transition-colors ${notificationFilter === 'all' ? 'bg-primary text-primary-foreground' : 'bg-muted text-text-secondary hover:bg-muted/80'}`,\n                  children: [\"All \", notifications.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1\",\n                    children: notifications.length\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 948,\n                    columnNumber: 56\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 940,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setNotificationFilter('unread'),\n                  className: `px-3 py-1 text-xs rounded-full transition-colors ${notificationFilter === 'unread' ? 'bg-primary text-primary-foreground' : 'bg-muted text-text-secondary hover:bg-muted/80'}`,\n                  children: [\"Unread \", unreadCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1\",\n                    children: unreadCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 958,\n                    columnNumber: 50\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 950,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => setNotificationFilter('high_priority'),\n                  className: `px-3 py-1 text-xs rounded-full transition-colors ${notificationFilter === 'high_priority' ? 'bg-primary text-primary-foreground' : 'bg-muted text-text-secondary hover:bg-muted/80'}`,\n                  children: [\"High Priority \", highPriorityCount > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"ml-1\",\n                    children: highPriorityCount\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 968,\n                    columnNumber: 63\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 939,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 916,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-h-96 overflow-y-auto\",\n              children: filteredNotifications.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-6 text-center text-text-secondary\",\n                children: [/*#__PURE__*/_jsxDEV(Icon, {\n                  name: \"Bell\",\n                  size: 32,\n                  className: \"mx-auto mb-3 opacity-50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 977,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium\",\n                  children: \"No notifications\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 978,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm mt-1\",\n                  children: notificationFilter === 'unread' ? 'All caught up!' : notificationFilter === 'high_priority' ? 'No high priority items' : 'You\\'re all set'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 979,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 976,\n                columnNumber: 21\n              }, this) : getRoleBasedNotifications(filteredNotifications, userRole).map(notification => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `p-4 border-b border-border last:border-b-0 hover:bg-muted/30 transition-colors ${!notification.isRead ? 'bg-primary/5 border-l-4 border-l-primary' : ''}`,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `p-2 rounded-full flex-shrink-0 ${notification.type === 'task_assignment' ? 'bg-blue-100 text-blue-600' : notification.type === 'meeting_invite' ? 'bg-green-100 text-green-600' : notification.type === 'meeting_reminder' ? 'bg-orange-100 text-orange-600' : notification.type === 'ai_suggestion' ? 'bg-purple-100 text-purple-600' : notification.type === 'project_update' ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600'}`,\n                    children: /*#__PURE__*/_jsxDEV(Icon, {\n                      name: getNotificationIcon(notification.type),\n                      size: 14\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1003,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start justify-between mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center space-x-2\",\n                          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                            className: `text-sm truncate ${!notification.isRead ? 'font-semibold text-text-primary' : 'font-medium text-text-primary'}`,\n                            children: notification.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1011,\n                            columnNumber: 35\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            className: `text-xs px-1.5 py-0.5 rounded ${getPriorityColor(notification.priority)} bg-muted`,\n                            children: notification.priority\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1014,\n                            columnNumber: 35\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1010,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-text-secondary text-xs mt-1\",\n                          children: formatTimestamp(notification.timestamp)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1018,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1009,\n                        columnNumber: 31\n                      }, this), !notification.isRead && /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2 mt-1\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1023,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1008,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-text-secondary text-sm mb-3 line-clamp-2\",\n                      children: notification.message\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1028,\n                      columnNumber: 29\n                    }, this), notification.type === 'task_assignment' && notification.data && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3 p-2 bg-muted/50 rounded-md\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-2 mb-2\",\n                        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                          src: notification.data.assignerAvatar,\n                          alt: notification.data.assignerName,\n                          className: \"w-5 h-5 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1036,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-text-secondary\",\n                          children: [\"Assigned by \", notification.data.assignerName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1041,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1035,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-text-secondary\",\n                        children: [\"Project: \", notification.data.projectName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1045,\n                        columnNumber: 33\n                      }, this), notification.data.dueDate && /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-text-secondary\",\n                        children: [\"Due: \", notification.data.dueDate.toLocaleDateString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1049,\n                        columnNumber: 35\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1034,\n                      columnNumber: 31\n                    }, this), notification.type === 'meeting_invite' && notification.data && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-3 p-2 bg-muted/50 rounded-md\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-2 mb-1\",\n                        children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                          src: notification.data.organizerAvatar,\n                          alt: notification.data.organizer,\n                          className: \"w-5 h-5 rounded-full\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1059,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"text-xs text-text-secondary\",\n                          children: [\"Organized by \", notification.data.organizer]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1064,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1058,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-text-secondary\",\n                        children: [notification.data.startTime.toLocaleDateString(), \" at \", notification.data.startTime.toLocaleTimeString([], {\n                          hour: '2-digit',\n                          minute: '2-digit'\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1068,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-text-secondary\",\n                        children: [\"Duration: \", notification.data.duration, \" minutes\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1071,\n                        columnNumber: 33\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1057,\n                      columnNumber: 31\n                    }, this), notification.actions && notification.actions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex flex-wrap gap-2\",\n                      children: notification.actions.map((action, index) => /*#__PURE__*/_jsxDEV(Button, {\n                        variant: action.variant === 'primary' ? 'default' : action.variant === 'danger' ? 'destructive' : 'outline',\n                        size: \"sm\",\n                        onClick: e => {\n                          e.stopPropagation();\n                          handleNotificationAction(notification.id, action.action, notification.data);\n                        },\n                        disabled: isLoading,\n                        className: \"text-xs h-7\",\n                        children: action.label\n                      }, index, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1081,\n                        columnNumber: 35\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1079,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1006,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 993,\n                  columnNumber: 25\n                }, this)\n              }, notification.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 987,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 974,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 border-t border-border\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                className: \"w-full text-primary hover:text-primary\",\n                onClick: () => {\n                  setIsNotificationDropdownOpen(false);\n                  // Navigate to full notifications page if it exists\n                  console.log('Navigate to full notifications page');\n                },\n                children: \"View all notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1106,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1105,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 914,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 882,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          ref: userDropdownRef,\n          children: [/*#__PURE__*/_jsxDEV(ProfessionalButton, {\n            variant: \"ghost\",\n            onClick: () => setIsUserDropdownOpen(!isUserDropdownOpen),\n            className: `\n                flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group\n                ${userRole === 'owner' ? 'hover:bg-purple-50 border border-purple-200/50' : 'hover:bg-gray-50'}\n              `,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `\n                w-9 h-9 rounded-full flex items-center justify-center font-semibold text-sm\n                transition-all duration-200 group-hover:scale-105\n                ${userRole === 'owner' ? 'bg-gradient-to-br from-purple-600 to-pink-600 text-white shadow-lg' : 'bg-gradient-to-br from-blue-600 to-indigo-600 text-white shadow-md'}\n              `,\n              children: user !== null && user !== void 0 && user.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"hidden md:block text-left\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm font-semibold text-gray-900\",\n                children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `\n                  text-xs font-medium\n                  ${userRole === 'owner' ? 'text-purple-600' : 'text-gray-500'}\n                `,\n                children: userRole === 'owner' ? 'Organization Owner' : userRole.charAt(0).toUpperCase() + userRole.slice(1)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1148,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1146,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"ChevronDown\",\n              size: 16,\n              className: `\n                  transition-all duration-200\n                  ${isUserDropdownOpen ? 'rotate-180' : ''}\n                  ${userRole === 'owner' ? 'text-purple-600' : 'text-gray-500'}\n                `\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1155,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1125,\n            columnNumber: 13\n          }, this), isUserDropdownOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `\n                absolute top-full right-0 mt-2 w-64 rounded-xl shadow-2xl z-50\n                animate-scale-in border backdrop-blur-sm\n                ${userRole === 'owner' ? 'bg-gradient-to-br from-white to-purple-50/50 border-purple-200' : 'bg-white border-gray-200'}\n              `,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `\n                    px-4 py-3 rounded-lg mb-4\n                    ${userRole === 'owner' ? 'bg-gradient-to-r from-purple-100/50 to-pink-100/50 border border-purple-200' : 'bg-gray-50 border border-gray-200'}\n                  `,\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `\n                        w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg\n                        ${userRole === 'owner' ? 'bg-gradient-to-br from-purple-600 to-pink-600 text-white' : 'bg-gradient-to-br from-blue-600 to-indigo-600 text-white'}\n                      `,\n                    children: user !== null && user !== void 0 && user.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1184,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-semibold text-gray-900\",\n                      children: (user === null || user === void 0 ? void 0 : user.name) || 'User'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1194,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-gray-600\",\n                      children: (user === null || user === void 0 ? void 0 : user.email) || '<EMAIL>'\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1195,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: `\n                          inline-block text-xs px-3 py-1 rounded-full font-medium mt-1\n                          ${userRole === 'owner' ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white' : userRole === 'admin' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700'}\n                        `,\n                      children: userRole === 'owner' ? 'Organization Owner' : userRole.charAt(0).toUpperCase() + userRole.slice(1)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1196,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1193,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1183,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1176,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"py-1\",\n                children: [/*#__PURE__*/_jsxDEV(Link, {\n                  to: \"/user-profile-settings\",\n                  className: \"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"User\",\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1215,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Profile Settings\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1216,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1211,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"Bell\",\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1219,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Notifications\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1220,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1218,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  className: \"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"HelpCircle\",\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1223,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Help & Support\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1224,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1222,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"border-t border-border my-1\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1226,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: handleLogout,\n                  className: \"w-full flex items-center space-x-2 px-2 py-2 text-sm text-destructive hover:bg-muted rounded-sm transition-micro\",\n                  children: [/*#__PURE__*/_jsxDEV(Icon, {\n                    name: \"LogOut\",\n                    size: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1231,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"Sign Out\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1232,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1227,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1210,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1175,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1167,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1124,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"ghost\",\n          size: \"icon\",\n          onClick: () => setIsMobileMenuOpen(!isMobileMenuOpen),\n          className: \"lg:hidden\",\n          children: /*#__PURE__*/_jsxDEV(Icon, {\n            name: isMobileMenuOpen ? \"X\" : \"Menu\",\n            size: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1247,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1241,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 880,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 545,\n      columnNumber: 7\n    }, this), isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      ref: mobileMenuRef,\n      className: \"lg:hidden border-t border-border bg-surface\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"px-4 py-2 space-y-1\",\n        children: [roleFeatures.canCreateProjects && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => {\n            navigate('/project-management');\n            setIsMobileMenuOpen(false);\n          },\n          className: \"w-full flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-micro\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Plus\",\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1265,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"New Project\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1266,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1258,\n          columnNumber: 15\n        }, this), navigationItems.map(item => /*#__PURE__*/_jsxDEV(Link, {\n          to: item.path,\n          onClick: () => setIsMobileMenuOpen(false),\n          className: `flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${isActivePath(item.path) ? 'bg-primary text-primary-foreground' : 'text-text-secondary hover:text-text-primary hover:bg-muted'}`,\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: item.icon,\n            size: 16\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1281,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1282,\n            columnNumber: 17\n          }, this)]\n        }, item.path, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1271,\n          columnNumber: 15\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1255,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1254,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(CreateProjectModal, {\n      isOpen: isCreateProjectModalOpen,\n      onClose: () => setIsCreateProjectModalOpen(false),\n      onCreateProject: handleCreateProjectSubmit,\n      organizationId: organization.id || 1,\n      organizationName: organization.name\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1290,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 538,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleBasedHeader, \"RuAZo/vuFoN+vcmW3oolvbobmNc=\", false, function () {\n  return [useLocation, useNavigate];\n});\n_c = RoleBasedHeader;\nexport default RoleBasedHeader;\nvar _c;\n$RefreshReg$(_c, \"RoleBasedHeader\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Link", "useLocation", "useNavigate", "Icon", "<PERSON><PERSON>", "ProfessionalButton", "OwnerActionButton", "IconButton", "Flex", "authService", "CreateProjectModal", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RoleBasedHeader", "userRole", "currentUser", "currentOrganization", "_s", "isOrgDropdownOpen", "setIsOrgDropdownOpen", "isProjectDropdownOpen", "setIsProjectDropdownOpen", "isUserDropdownOpen", "setIsUserDropdownOpen", "isMobileMenuOpen", "setIsMobileMenuOpen", "isNotificationDropdownOpen", "setIsNotificationDropdownOpen", "isCreateProjectModalOpen", "setIsCreateProjectModalOpen", "location", "navigate", "orgDropdownRef", "projectDropdownRef", "userDropdownRef", "mobileMenuRef", "notificationDropdownRef", "user", "name", "email", "avatar", "role", "organization", "domain", "logo", "availableOrganizations", "id", "currentProject", "setCurrentProject", "description", "status", "getAvailableProjects", "allProjects", "memberRole", "filter", "project", "availableProjects", "getNavigationItems", "baseItems", "label", "path", "icon", "roles", "adminItems", "ownerItems", "allItems", "item", "includes", "toLowerCase", "getRoleFeatures", "features", "viewer", "canCreateProjects", "canInviteMembers", "canManageSettings", "canViewAnalytics", "canManageBilling", "canSwitchOrganizations", "member", "admin", "owner", "navigationItems", "roleFeatures", "notifications", "setNotifications", "type", "title", "message", "timestamp", "Date", "now", "isRead", "priority", "data", "taskId", "taskTitle", "taskDescription", "projectName", "projectId", "assignerName", "assignerAvatar", "dueDate", "estimatedHours", "actions", "action", "variant", "meetingId", "meeting<PERSON>itle", "organizer", "organizer<PERSON><PERSON><PERSON>", "startTime", "duration", "attendees", "agenda", "suggestionType", "confidence", "reasoning", "recommendedActions", "impactAnalysis", "timesSaved", "teamEfficiency", "riskReduction", "deadlineDate", "completionPercentage", "remainingTasks", "assignedTo", "notificationFilter", "setNotificationFilter", "isLoading", "setIsLoading", "unreadCount", "n", "length", "highPriorityCount", "filteredNotifications", "notification", "mark<PERSON><PERSON><PERSON>", "notificationId", "prev", "map", "markAllAsRead", "getNotificationIcon", "getPriorityColor", "formatTimestamp", "diff", "minutes", "Math", "floor", "hours", "days", "toLocaleDateString", "getRoleBasedNotifications", "handleNotificationAction", "notificationData", "console", "log", "state", "highlightTaskId", "taskAccepted", "reason", "prompt", "window", "open", "error", "isActivePath", "pathname", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "handleOrganizationSwitch", "orgId", "handleProjectSwitch", "handleCreateProject", "handleCreateProjectSubmit", "projectData", "newProject", "handleLogout", "logout", "replace", "getRoleBadgeColor", "colors", "className", "children", "to", "viewBox", "fill", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "onClick", "char<PERSON>t", "size", "org", "src", "alt", "toLocaleTimeString", "hour", "minute", "index", "e", "stopPropagation", "disabled", "split", "join", "toUpperCase", "slice", "isOpen", "onClose", "onCreateProject", "organizationId", "organizationName", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/ui/RoleBasedHeader.jsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { Link, useLocation, useNavigate } from 'react-router-dom';\nimport Icon from '../AppIcon';\nimport Button from './Button';\nimport ProfessionalButton, { OwnerActionButton, IconButton } from './ProfessionalButton';\nimport { Flex } from './ProfessionalLayout';\nimport authService from '../../utils/authService';\nimport CreateProjectModal from '../modals/CreateProjectModal';\n\nconst RoleBasedHeader = ({ userRole = 'member', currentUser, currentOrganization }) => {\n  const [isOrgDropdownOpen, setIsOrgDropdownOpen] = useState(false);\n  const [isProjectDropdownOpen, setIsProjectDropdownOpen] = useState(false);\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] = useState(false);\n  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState(false);\n  const location = useLocation();\n  const navigate = useNavigate();\n  \n  const orgDropdownRef = useRef(null);\n  const projectDropdownRef = useRef(null);\n  const userDropdownRef = useRef(null);\n  const mobileMenuRef = useRef(null);\n  const notificationDropdownRef = useRef(null);\n\n  // Default user data if not provided\n  const user = {\n    name: 'Sarah Johnson',\n    email: '<EMAIL>',\n    avatar: '/assets/images/avatar.jpg',\n    role: 'Project Manager',\n    ...currentUser\n  };\n\n  // Default organization data if not provided\n  const organization = currentOrganization || {\n    name: 'Acme Corporation',\n    domain: 'acme.com',\n    logo: '/assets/images/org-logo.png'\n  };\n\n  const availableOrganizations = [\n    { id: 1, name: 'Acme Corporation', domain: 'acme.com', role: 'Admin' },\n    { id: 2, name: 'TechStart Inc', domain: 'techstart.com', role: 'Member' },\n    { id: 3, name: 'Global Solutions', domain: 'globalsol.com', role: 'Manager' }\n  ];\n\n  // Project state and data\n  const [currentProject, setCurrentProject] = useState({\n    id: 1,\n    name: 'Website Redesign',\n    description: 'Complete redesign of company website',\n    status: 'active'\n  });\n\n  // Mock project data based on user role\n  const getAvailableProjects = (role) => {\n    const allProjects = [\n      { id: 1, name: 'Website Redesign', description: 'Complete redesign of company website', status: 'active', memberRole: 'assigned' },\n      { id: 2, name: 'Mobile App Development', description: 'New mobile application for customers', status: 'active', memberRole: 'assigned' },\n      { id: 3, name: 'Marketing Campaign', description: 'Q4 marketing campaign planning', status: 'active', memberRole: 'not-assigned' },\n      { id: 4, name: 'Database Migration', description: 'Migrate to new database infrastructure', status: 'active', memberRole: 'not-assigned' },\n      { id: 5, name: 'Security Audit', description: 'Comprehensive security review', status: 'planning', memberRole: 'not-assigned' }\n    ];\n\n    // Members only see projects they're assigned to\n    if (role === 'member') {\n      return allProjects.filter(project => project.memberRole === 'assigned');\n    }\n\n    // Admins and Owners see all projects\n    return allProjects;\n  };\n\n  const availableProjects = getAvailableProjects(userRole);\n\n  // Role-based navigation configuration\n  const getNavigationItems = (role) => {\n    const baseItems = [\n      { label: 'Projects', path: '/kanban-board', icon: 'Kanban', roles: ['viewer', 'member', 'admin', 'owner'] },\n      { label: 'Team Members', path: '/team-members', icon: 'Users', roles: ['member', 'admin', 'owner'] }\n    ];\n\n    const adminItems = [\n      // Organization settings moved to owner-only items\n    ];\n\n    const ownerItems = [\n      { label: 'Organization', path: '/organization-settings', icon: 'Settings', roles: ['owner'] },\n      { label: 'Analytics', path: '/analytics', icon: 'BarChart3', roles: ['owner'] },\n      { label: 'Billing', path: '/billing', icon: 'CreditCard', roles: ['owner'] }\n    ];\n\n    // Filter items based on user role\n    const allItems = [...baseItems, ...adminItems, ...ownerItems];\n    return allItems.filter(item => item.roles.includes(role.toLowerCase()));\n  };\n\n  // Get role-specific features\n  const getRoleFeatures = (role) => {\n    const features = {\n      viewer: {\n        canCreateProjects: false,\n        canInviteMembers: false,\n        canManageSettings: false,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: false\n      },\n      member: {\n        canCreateProjects: true,\n        canInviteMembers: false,\n        canManageSettings: false,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: false\n      },\n      admin: {\n        canCreateProjects: true,\n        canInviteMembers: true,\n        canManageSettings: true,\n        canViewAnalytics: false,\n        canManageBilling: false,\n        canSwitchOrganizations: true\n      },\n      owner: {\n        canCreateProjects: true,\n        canInviteMembers: true,\n        canManageSettings: true,\n        canViewAnalytics: true,\n        canManageBilling: true,\n        canSwitchOrganizations: true\n      }\n    };\n    return features[role.toLowerCase()] || features.viewer;\n  };\n\n  const navigationItems = getNavigationItems(userRole);\n  const roleFeatures = getRoleFeatures(userRole);\n\n  // Enhanced notification data with comprehensive structure\n  const [notifications, setNotifications] = useState([\n    {\n      id: 'task_001',\n      type: 'task_assignment',\n      title: 'New Task Assigned',\n      message: 'You have been assigned to work on the payment gateway integration for the e-commerce platform.',\n      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago\n      isRead: false,\n      priority: 'high',\n      data: {\n        taskId: 'task_123',\n        taskTitle: 'Payment Gateway Integration',\n        taskDescription: 'Implement secure payment processing with Stripe API for the e-commerce platform. This includes setting up webhooks, handling payment failures, and ensuring PCI compliance.',\n        projectName: 'E-commerce Platform',\n        projectId: 'proj_001',\n        assignerName: 'Emily Davis',\n        assignerAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now\n        estimatedHours: 16\n      },\n      actions: [\n        { label: 'Accept Task', action: 'accept_task', variant: 'primary' },\n        { label: 'View Details', action: 'view_task', variant: 'secondary' },\n        { label: 'Decline', action: 'decline_task', variant: 'danger' }\n      ]\n    },\n    {\n      id: 'meeting_001',\n      type: 'meeting_invite',\n      title: 'Team Sprint Planning',\n      message: 'You are invited to the sprint planning meeting for the upcoming development cycle.',\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago\n      isRead: false,\n      priority: 'medium',\n      data: {\n        meetingId: 'meet_456',\n        meetingTitle: 'Sprint Planning - Q1 2025',\n        organizer: 'Sarah Johnson',\n        organizerAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n        startTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow\n        duration: 120, // minutes\n        location: 'Conference Room A / Zoom',\n        attendees: ['John Doe', 'Alice Smith', 'Bob Wilson'],\n        agenda: 'Review previous sprint, plan upcoming tasks, estimate story points'\n      },\n      actions: [\n        { label: 'Accept', action: 'accept_meeting', variant: 'primary' },\n        { label: 'Decline', action: 'decline_meeting', variant: 'secondary' },\n        { label: 'View Details', action: 'view_meeting', variant: 'secondary' }\n      ]\n    },\n    {\n      id: 'ai_001',\n      type: 'ai_suggestion',\n      title: 'AI Recommendation: Task Prioritization',\n      message: 'Based on your current workload and deadlines, I recommend prioritizing the API integration task.',\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago\n      isRead: true,\n      priority: 'low',\n      data: {\n        suggestionType: 'task_prioritization',\n        confidence: 0.85,\n        reasoning: 'The API integration task is blocking 3 other team members and has a deadline in 2 days.',\n        recommendedActions: [\n          'Move API integration to top priority',\n          'Allocate 6 hours today for this task',\n          'Consider requesting help from backend team'\n        ],\n        impactAnalysis: {\n          timesSaved: '4 hours',\n          teamEfficiency: '+15%',\n          riskReduction: 'High'\n        }\n      },\n      actions: [\n        { label: 'Apply Suggestion', action: 'apply_ai_suggestion', variant: 'primary' },\n        { label: 'Learn More', action: 'view_ai_details', variant: 'secondary' }\n      ]\n    },\n    {\n      id: 'deadline_001',\n      type: 'project_update',\n      title: 'Deadline Approaching',\n      message: 'The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.',\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago\n      isRead: true,\n      priority: 'high',\n      data: {\n        projectId: 'proj_002',\n        projectName: 'Mobile App Redesign',\n        deadlineDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now\n        completionPercentage: 75,\n        remainingTasks: 3,\n        assignedTo: 'Design Team'\n      },\n      actions: [\n        { label: 'View Project', action: 'view_project', variant: 'primary' },\n        { label: 'Update Status', action: 'update_status', variant: 'secondary' }\n      ]\n    }\n  ]);\n\n  // Notification state and filtering\n  const [notificationFilter, setNotificationFilter] = useState('all'); // 'all', 'unread', 'high_priority'\n  const [isLoading, setIsLoading] = useState(false);\n\n  const unreadCount = notifications.filter(n => !n.isRead).length;\n  const highPriorityCount = notifications.filter(n => n.priority === 'high' && !n.isRead).length;\n\n  // Filter notifications based on current filter\n  const filteredNotifications = notifications.filter(notification => {\n    switch (notificationFilter) {\n      case 'unread':\n        return !notification.isRead;\n      case 'high_priority':\n        return notification.priority === 'high';\n      default:\n        return true;\n    }\n  });\n\n  const markAsRead = (notificationId) => {\n    setNotifications(prev =>\n      prev.map(notification =>\n        notification.id === notificationId\n          ? { ...notification, isRead: true }\n          : notification\n      )\n    );\n  };\n\n  const markAllAsRead = () => {\n    setNotifications(prev =>\n      prev.map(notification => ({ ...notification, isRead: true }))\n    );\n  };\n\n  const getNotificationIcon = (type) => {\n    switch (type) {\n      case 'task_assignment': return 'UserPlus';\n      case 'meeting_invite': return 'Calendar';\n      case 'meeting_reminder': return 'Clock';\n      case 'ai_suggestion': return 'Zap';\n      case 'project_update': return 'AlertTriangle';\n      default: return 'Bell';\n    }\n  };\n\n  const getPriorityColor = (priority) => {\n    switch (priority) {\n      case 'high': return 'text-destructive';\n      case 'medium': return 'text-warning';\n      case 'low': return 'text-muted-foreground';\n      default: return 'text-muted-foreground';\n    }\n  };\n\n  const formatTimestamp = (timestamp) => {\n    const now = new Date();\n    const diff = now - timestamp;\n    const minutes = Math.floor(diff / (1000 * 60));\n    const hours = Math.floor(diff / (1000 * 60 * 60));\n    const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n\n    if (minutes < 1) return 'Just now';\n    if (minutes < 60) return `${minutes}m ago`;\n    if (hours < 24) return `${hours}h ago`;\n    if (days < 7) return `${days}d ago`;\n    return timestamp.toLocaleDateString();\n  };\n\n  // Role-based notification filtering\n  const getRoleBasedNotifications = (notifications, userRole) => {\n    return notifications.filter(notification => {\n      switch (notification.type) {\n        case 'meeting_invite':\n          // All roles can receive meeting invites\n          return true;\n        case 'ai_suggestion':\n          // AI suggestions available to all roles\n          return true;\n        case 'task_assignment':\n          // Task assignments for members and above only\n          // Viewers cannot receive task assignments in this system\n          return ['member', 'admin', 'owner'].includes(userRole?.toLowerCase());\n        case 'project_update':\n          // Project updates for all roles\n          return true;\n        default:\n          return true;\n      }\n    });\n  };\n\n  // Notification action handlers\n  const handleNotificationAction = async (notificationId, action, notificationData) => {\n    setIsLoading(true);\n\n    try {\n      switch (action) {\n        case 'accept_task':\n          // Accept task assignment\n          console.log('Accepting task:', notificationData.taskId);\n          // Navigate to kanban board with task highlighted\n          navigate('/kanban-board', {\n            state: {\n              projectId: notificationData.projectId,\n              highlightTaskId: notificationData.taskId,\n              taskAccepted: true\n            }\n          });\n          // Remove notification after acceptance\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n\n        case 'view_task':\n          // View task details\n          navigate('/kanban-board', {\n            state: {\n              projectId: notificationData.projectId,\n              highlightTaskId: notificationData.taskId\n            }\n          });\n          markAsRead(notificationId);\n          break;\n\n        case 'decline_task':\n          // Decline task assignment\n          const reason = prompt('Please provide a reason for declining this task (optional):');\n          console.log('Declining task:', notificationData.taskId, 'Reason:', reason);\n          // Remove notification after declining\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n\n        case 'accept_meeting':\n          // Accept meeting invitation\n          console.log('Accepting meeting:', notificationData.meetingId);\n          // Update meeting status and remove notification\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n\n        case 'decline_meeting':\n          // Decline meeting invitation\n          console.log('Declining meeting:', notificationData.meetingId);\n          setNotifications(prev => prev.filter(n => n.id !== notificationId));\n          break;\n\n        case 'view_meeting':\n          // View meeting details\n          console.log('Viewing meeting details:', notificationData.meetingId);\n          markAsRead(notificationId);\n          break;\n\n        case 'join_meeting':\n          // Join meeting (open meeting link)\n          console.log('Joining meeting:', notificationData.meetingId);\n          window.open('https://zoom.us/j/meeting-room', '_blank');\n          markAsRead(notificationId);\n          break;\n\n        case 'apply_ai_suggestion':\n          // Apply AI suggestion\n          console.log('Applying AI suggestion:', notificationData.suggestionType);\n          markAsRead(notificationId);\n          break;\n\n        case 'view_project':\n          // View project details\n          navigate('/project-overview', {\n            state: {\n              projectId: notificationData.projectId\n            }\n          });\n          markAsRead(notificationId);\n          break;\n\n        default:\n          console.log('Unknown action:', action);\n          markAsRead(notificationId);\n      }\n    } catch (error) {\n      console.error('Error handling notification action:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isActivePath = (path) => location.pathname === path;\n\n  // Close dropdowns when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (orgDropdownRef.current && !orgDropdownRef.current.contains(event.target)) {\n        setIsOrgDropdownOpen(false);\n      }\n      if (projectDropdownRef.current && !projectDropdownRef.current.contains(event.target)) {\n        setIsProjectDropdownOpen(false);\n      }\n      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {\n        setIsUserDropdownOpen(false);\n      }\n      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {\n        setIsMobileMenuOpen(false);\n      }\n      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target)) {\n        setIsNotificationDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleOrganizationSwitch = (orgId) => {\n    console.log('Switching to organization:', orgId);\n    setIsOrgDropdownOpen(false);\n  };\n\n  const handleProjectSwitch = (project) => {\n    console.log('Switching to project:', project);\n    setCurrentProject(project);\n    setIsProjectDropdownOpen(false);\n    // Navigate to the project overview page\n    navigate('/project-overview', { state: { project: project } });\n  };\n\n  const handleCreateProject = () => {\n    console.log('Creating new project...');\n    setIsProjectDropdownOpen(false);\n    setIsCreateProjectModalOpen(true);\n  };\n\n  const handleCreateProjectSubmit = async (projectData) => {\n    try {\n      console.log('Creating project:', projectData);\n      // Here you would typically call an API to create the project\n      // For now, we'll just add it to the available projects\n      const newProject = {\n        id: Date.now(),\n        name: projectData.name,\n        description: projectData.description,\n        status: 'active',\n        memberRole: 'assigned'\n      };\n\n      // Update current project to the newly created one\n      setCurrentProject(newProject);\n      setIsCreateProjectModalOpen(false);\n\n      // Navigate to the new project's overview page\n      navigate('/project-overview', { state: { project: newProject } });\n    } catch (error) {\n      console.error('Failed to create project:', error);\n      throw error;\n    }\n  };\n\n  const handleLogout = async () => {\n    try {\n      console.log('Logging out...');\n\n      // Clear authentication state\n      await authService.logout();\n\n      // Close any open dropdowns\n      setIsUserDropdownOpen(false);\n      setIsOrgDropdownOpen(false);\n      setIsProjectDropdownOpen(false);\n      setIsMobileMenuOpen(false);\n\n      // Redirect to login page\n      navigate('/login', { replace: true });\n    } catch (error) {\n      console.error('Error during logout:', error);\n      // Even if logout fails, redirect to login page\n      navigate('/login', { replace: true });\n    }\n  };\n\n  // Don't render header on auth pages\n  if (location.pathname === '/login' || location.pathname === '/register') {\n    return null;\n  }\n\n  // Get role badge color\n  const getRoleBadgeColor = (role) => {\n    const colors = {\n      viewer: 'bg-gray-100 text-gray-800',\n      member: 'bg-blue-100 text-blue-800',\n      admin: 'bg-purple-100 text-purple-800',\n      owner: 'bg-green-100 text-green-800'\n    };\n    return colors[role.toLowerCase()] || colors.viewer;\n  };\n\n  return (\n    <header className={`\n      fixed top-0 left-0 right-0 z-50 transition-all duration-300\n      ${userRole === 'owner'\n        ? 'bg-gradient-to-r from-white via-purple-50/40 to-white border-b border-purple-200/60 shadow-xl backdrop-blur-sm'\n        : 'bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-lg'\n      }\n    `}>\n      <div className=\"flex items-center justify-between h-16 px-4 lg:px-6 max-w-7xl mx-auto\">\n        {/* Logo */}\n        <div className=\"flex items-center\">\n          <Link to=\"/kanban-board\" className=\"flex items-center space-x-3 group transition-all duration-200 hover:scale-105\">\n            <div className={`\n              w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-200\n              ${userRole === 'owner'\n                ? 'bg-gradient-to-br from-purple-600 to-pink-600 shadow-lg group-hover:shadow-xl'\n                : 'bg-gradient-to-br from-blue-600 to-indigo-600 shadow-md group-hover:shadow-lg'\n              }\n            `}>\n              <svg viewBox=\"0 0 24 24\" className=\"w-6 h-6 text-white\" fill=\"currentColor\">\n                <path d=\"M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z\"/>\n              </svg>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className={`\n                text-xl font-bold transition-colors duration-200\n                ${userRole === 'owner'\n                  ? 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent'\n                  : 'text-gray-900'\n                }\n              `}>\n                Agno WorkSphere\n              </span>\n              {userRole === 'owner' && (\n                <span className=\"text-xs font-medium text-purple-600 -mt-1\">\n                  Owner Console\n                </span>\n              )}\n            </div>\n          </Link>\n        </div>\n\n        {/* Organization Context Switcher - Desktop */}\n        <div className=\"hidden lg:flex items-center space-x-6\">\n          {roleFeatures.canSwitchOrganizations ? (\n            <div className=\"relative\" ref={orgDropdownRef}>\n              <ProfessionalButton\n                variant={userRole === 'owner' ? 'owner' : 'ghost'}\n                onClick={() => setIsOrgDropdownOpen(!isOrgDropdownOpen)}\n                className=\"flex items-center space-x-3 px-4 py-2.5 transition-all duration-200\"\n              >\n                <div className={`\n                  w-7 h-7 rounded-lg flex items-center justify-center font-semibold text-sm\n                  ${userRole === 'owner'\n                    ? 'bg-gradient-to-br from-purple-100 to-pink-100 text-purple-700'\n                    : 'bg-gray-100 text-gray-700'\n                  }\n                `}>\n                  {organization.name.charAt(0)}\n                </div>\n                <div className=\"flex flex-col items-start\">\n                  <span className=\"font-semibold text-sm\">{organization.name}</span>\n                  {userRole === 'owner' && (\n                    <span className=\"text-xs text-purple-600\">Organization Owner</span>\n                  )}\n                </div>\n                <Icon name=\"ChevronDown\" size={16} className={`\n                  transition-transform duration-200\n                  ${isOrgDropdownOpen ? 'rotate-180' : ''}\n                  ${userRole === 'owner' ? 'text-purple-600' : 'text-gray-500'}\n                `} />\n              </ProfessionalButton>\n\n              {isOrgDropdownOpen && (\n                <div className={`\n                  absolute top-full left-0 mt-2 w-72 rounded-xl shadow-2xl z-50\n                  animate-scale-in border backdrop-blur-sm\n                  ${userRole === 'owner'\n                    ? 'bg-gradient-to-br from-white to-purple-50/50 border-purple-200'\n                    : 'bg-white border-gray-200'\n                  }\n                `}>\n                  <div className=\"p-3\">\n                    <div className={`\n                      text-xs font-semibold uppercase tracking-wider px-3 py-2 rounded-lg\n                      ${userRole === 'owner'\n                        ? 'text-purple-700 bg-purple-100/50'\n                        : 'text-gray-600 bg-gray-100'\n                      }\n                    `}>\n                      Switch Organization\n                    </div>\n                    <div className=\"mt-2 space-y-1\">\n                      {availableOrganizations.map((org) => (\n                        <button\n                          key={org.id}\n                          onClick={() => handleOrganizationSwitch(org.id)}\n                          className={`\n                            w-full flex items-center space-x-3 px-3 py-3 rounded-lg\n                            transition-all duration-200 text-left group\n                            ${userRole === 'owner'\n                              ? 'hover:bg-purple-100/50 hover:shadow-md'\n                              : 'hover:bg-gray-50 hover:shadow-sm'\n                            }\n                          `}\n                        >\n                          <div className={`\n                            w-10 h-10 rounded-lg flex items-center justify-center font-semibold text-sm\n                            ${userRole === 'owner'\n                              ? 'bg-gradient-to-br from-purple-100 to-pink-100 text-purple-700'\n                              : 'bg-gray-100 text-gray-700'\n                            }\n                          `}>\n                            {org.name.charAt(0)}\n                          </div>\n                          <div className=\"flex-1\">\n                            <div className=\"text-sm font-semibold text-gray-900\">{org.name}</div>\n                            <div className=\"text-xs text-gray-500\">{org.domain}</div>\n                          </div>\n                          <div className=\"flex items-center space-x-2\">\n                            <span className={`\n                              text-xs px-2 py-1 rounded-full font-medium\n                              ${org.role === 'owner'\n                                ? 'bg-purple-100 text-purple-700'\n                                : org.role === 'admin'\n                                ? 'bg-blue-100 text-blue-700'\n                                : 'bg-gray-100 text-gray-700'\n                              }\n                            `}>\n                              {org.role}\n                            </span>\n                            {org.id === 1 && (\n                              <Icon name=\"Check\" size={16} className=\"text-green-600\" />\n                            )}\n                          </div>\n                        </button>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          ) : (\n            // For members and viewers - show organization name without dropdown\n            <div className=\"flex items-center space-x-2 px-3 py-2\">\n              <div className=\"w-6 h-6 bg-muted rounded-sm flex items-center justify-center\">\n                <span className=\"text-xs font-medium text-text-primary\">\n                  {organization.name.charAt(0)}\n                </span>\n              </div>\n              <span className=\"font-medium text-text-primary\">{organization.name}</span>\n            </div>\n          )}\n\n          {/* Role-Based Navigation */}\n          <nav className=\"flex items-center space-x-1\">\n            {navigationItems.map((item) => {\n              // Special handling for Projects item - make it a dropdown\n              if (item.label === 'Projects') {\n                return (\n                  <div key={item.path} className=\"relative\" ref={projectDropdownRef}>\n                    <ProfessionalButton\n                      variant={userRole === 'owner' ? 'outline' : 'ghost'}\n                      onClick={() => setIsProjectDropdownOpen(!isProjectDropdownOpen)}\n                      className={`\n                        flex items-center space-x-2 px-4 py-2.5 rounded-lg text-sm font-medium\n                        transition-all duration-200 group\n                        ${isActivePath(item.path)\n                          ? userRole === 'owner'\n                            ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'\n                            : 'bg-blue-600 text-white shadow-md'\n                          : userRole === 'owner'\n                            ? 'text-purple-700 hover:bg-purple-50 border-purple-200'\n                            : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                        }\n                      `}\n                    >\n                      <Icon name={item.icon} size={16} />\n                      <span>{item.label}</span>\n                      <Icon\n                        name=\"ChevronDown\"\n                        size={12}\n                        className={`\n                          ml-1 transition-transform duration-200\n                          ${isProjectDropdownOpen ? 'rotate-180' : ''}\n                        `}\n                      />\n                    </ProfessionalButton>\n\n                    {isProjectDropdownOpen && (\n                      <div className={`\n                        absolute top-full left-0 mt-2 w-80 rounded-xl shadow-2xl z-50\n                        animate-scale-in border backdrop-blur-sm\n                        ${userRole === 'owner'\n                          ? 'bg-gradient-to-br from-white to-purple-50/50 border-purple-200'\n                          : 'bg-white border-gray-200'\n                        }\n                      `}>\n                        <div className=\"p-4\">\n                          <div className={`\n                            text-xs font-semibold uppercase tracking-wider px-3 py-2 rounded-lg mb-3\n                            ${userRole === 'owner'\n                              ? 'text-purple-700 bg-purple-100/50'\n                              : 'text-gray-600 bg-gray-100'\n                            }\n                          `}>\n                            Switch Project\n                          </div>\n\n                          {/* Create New Project - Only for Admins and Owners */}\n                          {(userRole === 'admin' || userRole === 'owner') && (\n                            <>\n                              <button\n                                onClick={handleCreateProject}\n                                className={`\n                                  w-full flex items-center space-x-3 px-3 py-3 rounded-lg\n                                  transition-all duration-200 text-left group mb-3\n                                  border-b pb-3\n                                  ${userRole === 'owner'\n                                    ? 'hover:bg-purple-100/50 hover:shadow-md border-purple-200'\n                                    : 'hover:bg-gray-50 hover:shadow-sm border-gray-200'\n                                  }\n                                `}\n                              >\n                                <div className={`\n                                  w-10 h-10 rounded-lg flex items-center justify-center\n                                  ${userRole === 'owner'\n                                    ? 'bg-gradient-to-br from-purple-500 to-pink-500 text-white'\n                                    : 'bg-gradient-to-br from-blue-500 to-indigo-500 text-white'\n                                  }\n                                  group-hover:scale-110 transition-transform duration-200\n                                `}>\n                                  <Icon name=\"Plus\" size={18} />\n                                </div>\n                                <div className=\"flex-1\">\n                                  <div className={`\n                                    text-sm font-semibold\n                                    ${userRole === 'owner' ? 'text-purple-700' : 'text-blue-700'}\n                                  `}>\n                                    Create New Project\n                                  </div>\n                                  <div className=\"text-xs text-gray-500\">Start a new project with AI setup</div>\n                                </div>\n                                <Icon\n                                  name=\"ArrowRight\"\n                                  size={16}\n                                  className={`\n                                    transition-transform duration-200 group-hover:translate-x-1\n                                    ${userRole === 'owner' ? 'text-purple-600' : 'text-blue-600'}\n                                  `}\n                                />\n                              </button>\n                            </>\n                          )}\n\n                          {/* Available Projects */}\n                          <div className=\"space-y-1\">\n                            {availableProjects.map((project) => (\n                              <button\n                                key={project.id}\n                                onClick={() => handleProjectSwitch(project)}\n                                className={`\n                                  w-full flex items-center space-x-3 px-3 py-3 rounded-lg\n                                  transition-all duration-200 text-left group\n                                  ${userRole === 'owner'\n                                    ? 'hover:bg-purple-100/50 hover:shadow-md'\n                                    : 'hover:bg-gray-50 hover:shadow-sm'\n                                  }\n                                `}\n                              >\n                                <div className={`\n                                  w-10 h-10 rounded-lg flex items-center justify-center\n                                  ${userRole === 'owner'\n                                    ? 'bg-gradient-to-br from-purple-100 to-pink-100 text-purple-600'\n                                    : 'bg-gray-100 text-gray-600'\n                                  }\n                                `}>\n                                  <Icon name=\"Folder\" size={18} />\n                                </div>\n                                <div className=\"flex-1\">\n                                  <div className=\"text-sm font-semibold text-gray-900\">{project.name}</div>\n                                  <div className=\"text-xs text-gray-500 truncate\">{project.description}</div>\n                                </div>\n                                <div className=\"flex flex-col items-end space-y-1\">\n                                  <span className={`\n                                    text-xs px-2 py-1 rounded-full font-medium\n                                    ${project.status === 'active'\n                                      ? 'bg-green-100 text-green-700'\n                                      : project.status === 'planning'\n                                      ? 'bg-yellow-100 text-yellow-700'\n                                      : 'bg-gray-100 text-gray-700'\n                                    }\n                                  `}>\n                                    {project.status}\n                                  </span>\n                                  {userRole === 'member' && (\n                                    <span className=\"text-xs text-gray-500\">Assigned</span>\n                                  )}\n                                  {project.id === currentProject.id && (\n                                    <Icon name=\"Check\" size={16} className=\"text-green-600\" />\n                                  )}\n                                </div>\n                              </button>\n                            ))}\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                );\n              }\n\n              // Regular navigation items\n              return (\n                <Link\n                  key={item.path}\n                  to={item.path}\n                  className={`\n                    flex items-center space-x-2 px-4 py-2.5 rounded-lg text-sm font-medium\n                    transition-all duration-200 group\n                    ${isActivePath(item.path)\n                      ? userRole === 'owner'\n                        ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg'\n                        : 'bg-blue-600 text-white shadow-md'\n                      : userRole === 'owner'\n                        ? 'text-purple-700 hover:bg-purple-50 hover:text-purple-800'\n                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'\n                    }\n                  `}\n                >\n                  <Icon\n                    name={item.icon}\n                    size={16}\n                    className=\"group-hover:scale-110 transition-transform duration-200\"\n                  />\n                  <span>{item.label}</span>\n                </Link>\n              );\n            })}\n          </nav>\n        </div>\n\n        {/* User Profile & Mobile Menu */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Notifications */}\n          <div className=\"relative\" ref={notificationDropdownRef}>\n            <ProfessionalButton\n              variant=\"ghost\"\n              onClick={() => setIsNotificationDropdownOpen(!isNotificationDropdownOpen)}\n              className={`\n                relative p-2.5 rounded-lg transition-all duration-200 group\n                ${userRole === 'owner'\n                  ? 'hover:bg-purple-50 text-purple-600'\n                  : 'hover:bg-gray-50 text-gray-600'\n                }\n              `}\n            >\n              <Icon\n                name=\"Bell\"\n                size={20}\n                className=\"group-hover:scale-110 transition-transform duration-200\"\n              />\n              {unreadCount > 0 && (\n                <span className={`\n                  absolute -top-1 -right-1 h-5 w-5 text-xs font-bold rounded-full\n                  flex items-center justify-center animate-pulse\n                  ${userRole === 'owner'\n                    ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'\n                    : 'bg-red-500 text-white'\n                  }\n                `}>\n                  {unreadCount > 9 ? '9+' : unreadCount}\n                </span>\n              )}\n            </ProfessionalButton>\n\n            {isNotificationDropdownOpen && (\n              <div className=\"absolute top-full right-0 mt-1 w-96 bg-popover border border-border rounded-md shadow-elevated z-1010 md:w-80\">\n                {/* Header with filters */}\n                <div className=\"p-4 border-b border-border\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <h3 className=\"font-semibold text-text-primary flex items-center\">\n                      Notifications\n                      {unreadCount > 0 && (\n                        <span className=\"ml-2 bg-destructive text-destructive-foreground text-xs px-2 py-1 rounded-full\">\n                          {unreadCount}\n                        </span>\n                      )}\n                    </h3>\n                    {unreadCount > 0 && (\n                      <Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                        onClick={markAllAsRead}\n                        className=\"text-xs text-primary hover:text-primary\"\n                      >\n                        Mark all read\n                      </Button>\n                    )}\n                  </div>\n\n                  {/* Filter tabs */}\n                  <div className=\"flex space-x-1\">\n                    <button\n                      onClick={() => setNotificationFilter('all')}\n                      className={`px-3 py-1 text-xs rounded-full transition-colors ${\n                        notificationFilter === 'all'\n                          ? 'bg-primary text-primary-foreground'\n                          : 'bg-muted text-text-secondary hover:bg-muted/80'\n                      }`}\n                    >\n                      All {notifications.length > 0 && <span className=\"ml-1\">{notifications.length}</span>}\n                    </button>\n                    <button\n                      onClick={() => setNotificationFilter('unread')}\n                      className={`px-3 py-1 text-xs rounded-full transition-colors ${\n                        notificationFilter === 'unread'\n                          ? 'bg-primary text-primary-foreground'\n                          : 'bg-muted text-text-secondary hover:bg-muted/80'\n                      }`}\n                    >\n                      Unread {unreadCount > 0 && <span className=\"ml-1\">{unreadCount}</span>}\n                    </button>\n                    <button\n                      onClick={() => setNotificationFilter('high_priority')}\n                      className={`px-3 py-1 text-xs rounded-full transition-colors ${\n                        notificationFilter === 'high_priority'\n                          ? 'bg-primary text-primary-foreground'\n                          : 'bg-muted text-text-secondary hover:bg-muted/80'\n                      }`}\n                    >\n                      High Priority {highPriorityCount > 0 && <span className=\"ml-1\">{highPriorityCount}</span>}\n                    </button>\n                  </div>\n                </div>\n\n                {/* Notifications list */}\n                <div className=\"max-h-96 overflow-y-auto\">\n                  {filteredNotifications.length === 0 ? (\n                    <div className=\"p-6 text-center text-text-secondary\">\n                      <Icon name=\"Bell\" size={32} className=\"mx-auto mb-3 opacity-50\" />\n                      <p className=\"font-medium\">No notifications</p>\n                      <p className=\"text-sm mt-1\">\n                        {notificationFilter === 'unread' ? 'All caught up!' :\n                         notificationFilter === 'high_priority' ? 'No high priority items' :\n                         'You\\'re all set'}\n                      </p>\n                    </div>\n                  ) : (\n                    getRoleBasedNotifications(filteredNotifications, userRole).map((notification) => (\n                      <div\n                        key={notification.id}\n                        className={`p-4 border-b border-border last:border-b-0 hover:bg-muted/30 transition-colors ${\n                          !notification.isRead ? 'bg-primary/5 border-l-4 border-l-primary' : ''\n                        }`}\n                      >\n                        <div className=\"flex items-start space-x-3\">\n                          {/* Notification icon */}\n                          <div className={`p-2 rounded-full flex-shrink-0 ${\n                            notification.type === 'task_assignment' ? 'bg-blue-100 text-blue-600' :\n                            notification.type === 'meeting_invite' ? 'bg-green-100 text-green-600' :\n                            notification.type === 'meeting_reminder' ? 'bg-orange-100 text-orange-600' :\n                            notification.type === 'ai_suggestion' ? 'bg-purple-100 text-purple-600' :\n                            notification.type === 'project_update' ? 'bg-red-100 text-red-600' :\n                            'bg-gray-100 text-gray-600'\n                          }`}>\n                            <Icon name={getNotificationIcon(notification.type)} size={14} />\n                          </div>\n\n                          <div className=\"flex-1 min-w-0\">\n                            {/* Header */}\n                            <div className=\"flex items-start justify-between mb-2\">\n                              <div className=\"flex-1\">\n                                <div className=\"flex items-center space-x-2\">\n                                  <p className={`text-sm truncate ${!notification.isRead ? 'font-semibold text-text-primary' : 'font-medium text-text-primary'}`}>\n                                    {notification.title}\n                                  </p>\n                                  <span className={`text-xs px-1.5 py-0.5 rounded ${getPriorityColor(notification.priority)} bg-muted`}>\n                                    {notification.priority}\n                                  </span>\n                                </div>\n                                <p className=\"text-text-secondary text-xs mt-1\">\n                                  {formatTimestamp(notification.timestamp)}\n                                </p>\n                              </div>\n                              {!notification.isRead && (\n                                <div className=\"w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2 mt-1\"></div>\n                              )}\n                            </div>\n\n                            {/* Message */}\n                            <p className=\"text-text-secondary text-sm mb-3 line-clamp-2\">\n                              {notification.message}\n                            </p>\n\n                            {/* Type-specific content */}\n                            {notification.type === 'task_assignment' && notification.data && (\n                              <div className=\"mb-3 p-2 bg-muted/50 rounded-md\">\n                                <div className=\"flex items-center space-x-2 mb-2\">\n                                  <img\n                                    src={notification.data.assignerAvatar}\n                                    alt={notification.data.assignerName}\n                                    className=\"w-5 h-5 rounded-full\"\n                                  />\n                                  <span className=\"text-xs text-text-secondary\">\n                                    Assigned by {notification.data.assignerName}\n                                  </span>\n                                </div>\n                                <p className=\"text-xs text-text-secondary\">\n                                  Project: {notification.data.projectName}\n                                </p>\n                                {notification.data.dueDate && (\n                                  <p className=\"text-xs text-text-secondary\">\n                                    Due: {notification.data.dueDate.toLocaleDateString()}\n                                  </p>\n                                )}\n                              </div>\n                            )}\n\n                            {notification.type === 'meeting_invite' && notification.data && (\n                              <div className=\"mb-3 p-2 bg-muted/50 rounded-md\">\n                                <div className=\"flex items-center space-x-2 mb-1\">\n                                  <img\n                                    src={notification.data.organizerAvatar}\n                                    alt={notification.data.organizer}\n                                    className=\"w-5 h-5 rounded-full\"\n                                  />\n                                  <span className=\"text-xs text-text-secondary\">\n                                    Organized by {notification.data.organizer}\n                                  </span>\n                                </div>\n                                <p className=\"text-xs text-text-secondary\">\n                                  {notification.data.startTime.toLocaleDateString()} at {notification.data.startTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}\n                                </p>\n                                <p className=\"text-xs text-text-secondary\">\n                                  Duration: {notification.data.duration} minutes\n                                </p>\n                              </div>\n                            )}\n\n                            {/* Action buttons */}\n                            {notification.actions && notification.actions.length > 0 && (\n                              <div className=\"flex flex-wrap gap-2\">\n                                {notification.actions.map((action, index) => (\n                                  <Button\n                                    key={index}\n                                    variant={action.variant === 'primary' ? 'default' : action.variant === 'danger' ? 'destructive' : 'outline'}\n                                    size=\"sm\"\n                                    onClick={(e) => {\n                                      e.stopPropagation();\n                                      handleNotificationAction(notification.id, action.action, notification.data);\n                                    }}\n                                    disabled={isLoading}\n                                    className=\"text-xs h-7\"\n                                  >\n                                    {action.label}\n                                  </Button>\n                                ))}\n                              </div>\n                            )}\n                          </div>\n                        </div>\n                      </div>\n                    ))\n                  )}\n                </div>\n\n                {/* Footer */}\n                <div className=\"p-3 border-t border-border\">\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"w-full text-primary hover:text-primary\"\n                    onClick={() => {\n                      setIsNotificationDropdownOpen(false);\n                      // Navigate to full notifications page if it exists\n                      console.log('Navigate to full notifications page');\n                    }}\n                  >\n                    View all notifications\n                  </Button>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* User Profile Dropdown */}\n          <div className=\"relative\" ref={userDropdownRef}>\n            <ProfessionalButton\n              variant=\"ghost\"\n              onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}\n              className={`\n                flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group\n                ${userRole === 'owner'\n                  ? 'hover:bg-purple-50 border border-purple-200/50'\n                  : 'hover:bg-gray-50'\n                }\n              `}\n            >\n              <div className={`\n                w-9 h-9 rounded-full flex items-center justify-center font-semibold text-sm\n                transition-all duration-200 group-hover:scale-105\n                ${userRole === 'owner'\n                  ? 'bg-gradient-to-br from-purple-600 to-pink-600 text-white shadow-lg'\n                  : 'bg-gradient-to-br from-blue-600 to-indigo-600 text-white shadow-md'\n                }\n              `}>\n                {user?.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'}\n              </div>\n              <div className=\"hidden md:block text-left\">\n                <div className=\"text-sm font-semibold text-gray-900\">{user?.name || 'User'}</div>\n                <div className={`\n                  text-xs font-medium\n                  ${userRole === 'owner' ? 'text-purple-600' : 'text-gray-500'}\n                `}>\n                  {userRole === 'owner' ? 'Organization Owner' : userRole.charAt(0).toUpperCase() + userRole.slice(1)}\n                </div>\n              </div>\n              <Icon\n                name=\"ChevronDown\"\n                size={16}\n                className={`\n                  transition-all duration-200\n                  ${isUserDropdownOpen ? 'rotate-180' : ''}\n                  ${userRole === 'owner' ? 'text-purple-600' : 'text-gray-500'}\n                `}\n              />\n            </ProfessionalButton>\n\n            {isUserDropdownOpen && (\n              <div className={`\n                absolute top-full right-0 mt-2 w-64 rounded-xl shadow-2xl z-50\n                animate-scale-in border backdrop-blur-sm\n                ${userRole === 'owner'\n                  ? 'bg-gradient-to-br from-white to-purple-50/50 border-purple-200'\n                  : 'bg-white border-gray-200'\n                }\n              `}>\n                <div className=\"p-4\">\n                  <div className={`\n                    px-4 py-3 rounded-lg mb-4\n                    ${userRole === 'owner'\n                      ? 'bg-gradient-to-r from-purple-100/50 to-pink-100/50 border border-purple-200'\n                      : 'bg-gray-50 border border-gray-200'\n                    }\n                  `}>\n                    <div className=\"flex items-center space-x-3\">\n                      <div className={`\n                        w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg\n                        ${userRole === 'owner'\n                          ? 'bg-gradient-to-br from-purple-600 to-pink-600 text-white'\n                          : 'bg-gradient-to-br from-blue-600 to-indigo-600 text-white'\n                        }\n                      `}>\n                        {user?.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'}\n                      </div>\n                      <div className=\"flex-1\">\n                        <div className=\"font-semibold text-gray-900\">{user?.name || 'User'}</div>\n                        <div className=\"text-sm text-gray-600\">{user?.email || '<EMAIL>'}</div>\n                        <span className={`\n                          inline-block text-xs px-3 py-1 rounded-full font-medium mt-1\n                          ${userRole === 'owner'\n                            ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white'\n                            : userRole === 'admin'\n                            ? 'bg-blue-100 text-blue-700'\n                            : 'bg-gray-100 text-gray-700'\n                          }\n                        `}>\n                          {userRole === 'owner' ? 'Organization Owner' : userRole.charAt(0).toUpperCase() + userRole.slice(1)}\n                        </span>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"py-1\">\n                    <Link \n                      to=\"/user-profile-settings\"\n                      className=\"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\"\n                    >\n                      <Icon name=\"User\" size={16} />\n                      <span>Profile Settings</span>\n                    </Link>\n                    <button className=\"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\">\n                      <Icon name=\"Bell\" size={16} />\n                      <span>Notifications</span>\n                    </button>\n                    <button className=\"w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro\">\n                      <Icon name=\"HelpCircle\" size={16} />\n                      <span>Help & Support</span>\n                    </button>\n                    <div className=\"border-t border-border my-1\"></div>\n                    <button \n                      onClick={handleLogout}\n                      className=\"w-full flex items-center space-x-2 px-2 py-2 text-sm text-destructive hover:bg-muted rounded-sm transition-micro\"\n                    >\n                      <Icon name=\"LogOut\" size={16} />\n                      <span>Sign Out</span>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile Menu Toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden\"\n          >\n            <Icon name={isMobileMenuOpen ? \"X\" : \"Menu\"} size={20} />\n          </Button>\n        </div>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMobileMenuOpen && (\n        <div ref={mobileMenuRef} className=\"lg:hidden border-t border-border bg-surface\">\n          <div className=\"px-4 py-2 space-y-1\">\n            {/* Create Project Button for Mobile */}\n            {roleFeatures.canCreateProjects && (\n              <button\n                onClick={() => {\n                  navigate('/project-management');\n                  setIsMobileMenuOpen(false);\n                }}\n                className=\"w-full flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-micro\"\n              >\n                <Icon name=\"Plus\" size={16} />\n                <span>New Project</span>\n              </button>\n            )}\n\n            {navigationItems.map((item) => (\n              <Link\n                key={item.path}\n                to={item.path}\n                onClick={() => setIsMobileMenuOpen(false)}\n                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${\n                  isActivePath(item.path)\n                    ? 'bg-primary text-primary-foreground'\n                    : 'text-text-secondary hover:text-text-primary hover:bg-muted'\n                }`}\n              >\n                <Icon name={item.icon} size={16} />\n                <span>{item.label}</span>\n              </Link>\n            ))}\n          </div>\n        </div>\n      )}\n\n      {/* Create Project Modal */}\n      <CreateProjectModal\n        isOpen={isCreateProjectModalOpen}\n        onClose={() => setIsCreateProjectModalOpen(false)}\n        onCreateProject={handleCreateProjectSubmit}\n        organizationId={organization.id || 1}\n        organizationName={organization.name}\n      />\n    </header>\n  );\n};\n\nexport default RoleBasedHeader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,kBAAkB,IAAIC,iBAAiB,EAAEC,UAAU,QAAQ,sBAAsB;AACxF,SAASC,IAAI,QAAQ,sBAAsB;AAC3C,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,kBAAkB,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9D,MAAMC,eAAe,GAAGA,CAAC;EAAEC,QAAQ,GAAG,QAAQ;EAAEC,WAAW;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyB,qBAAqB,EAAEC,wBAAwB,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAAC2B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC+B,0BAA0B,EAAEC,6BAA6B,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EACnF,MAAM,CAACiC,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAMmC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAC9B,MAAMgC,QAAQ,GAAG/B,WAAW,CAAC,CAAC;EAE9B,MAAMgC,cAAc,GAAGpC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMqC,kBAAkB,GAAGrC,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMsC,eAAe,GAAGtC,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMuC,aAAa,GAAGvC,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMwC,uBAAuB,GAAGxC,MAAM,CAAC,IAAI,CAAC;;EAE5C;EACA,MAAMyC,IAAI,GAAG;IACXC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,2BAA2B;IAClCC,MAAM,EAAE,2BAA2B;IACnCC,IAAI,EAAE,iBAAiB;IACvB,GAAG1B;EACL,CAAC;;EAED;EACA,MAAM2B,YAAY,GAAG1B,mBAAmB,IAAI;IAC1CsB,IAAI,EAAE,kBAAkB;IACxBK,MAAM,EAAE,UAAU;IAClBC,IAAI,EAAE;EACR,CAAC;EAED,MAAMC,sBAAsB,GAAG,CAC7B;IAAEC,EAAE,EAAE,CAAC;IAAER,IAAI,EAAE,kBAAkB;IAAEK,MAAM,EAAE,UAAU;IAAEF,IAAI,EAAE;EAAQ,CAAC,EACtE;IAAEK,EAAE,EAAE,CAAC;IAAER,IAAI,EAAE,eAAe;IAAEK,MAAM,EAAE,eAAe;IAAEF,IAAI,EAAE;EAAS,CAAC,EACzE;IAAEK,EAAE,EAAE,CAAC;IAAER,IAAI,EAAE,kBAAkB;IAAEK,MAAM,EAAE,eAAe;IAAEF,IAAI,EAAE;EAAU,CAAC,CAC9E;;EAED;EACA,MAAM,CAACM,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC;IACnDmD,EAAE,EAAE,CAAC;IACLR,IAAI,EAAE,kBAAkB;IACxBW,WAAW,EAAE,sCAAsC;IACnDC,MAAM,EAAE;EACV,CAAC,CAAC;;EAEF;EACA,MAAMC,oBAAoB,GAAIV,IAAI,IAAK;IACrC,MAAMW,WAAW,GAAG,CAClB;MAAEN,EAAE,EAAE,CAAC;MAAER,IAAI,EAAE,kBAAkB;MAAEW,WAAW,EAAE,sCAAsC;MAAEC,MAAM,EAAE,QAAQ;MAAEG,UAAU,EAAE;IAAW,CAAC,EAClI;MAAEP,EAAE,EAAE,CAAC;MAAER,IAAI,EAAE,wBAAwB;MAAEW,WAAW,EAAE,sCAAsC;MAAEC,MAAM,EAAE,QAAQ;MAAEG,UAAU,EAAE;IAAW,CAAC,EACxI;MAAEP,EAAE,EAAE,CAAC;MAAER,IAAI,EAAE,oBAAoB;MAAEW,WAAW,EAAE,gCAAgC;MAAEC,MAAM,EAAE,QAAQ;MAAEG,UAAU,EAAE;IAAe,CAAC,EAClI;MAAEP,EAAE,EAAE,CAAC;MAAER,IAAI,EAAE,oBAAoB;MAAEW,WAAW,EAAE,wCAAwC;MAAEC,MAAM,EAAE,QAAQ;MAAEG,UAAU,EAAE;IAAe,CAAC,EAC1I;MAAEP,EAAE,EAAE,CAAC;MAAER,IAAI,EAAE,gBAAgB;MAAEW,WAAW,EAAE,+BAA+B;MAAEC,MAAM,EAAE,UAAU;MAAEG,UAAU,EAAE;IAAe,CAAC,CAChI;;IAED;IACA,IAAIZ,IAAI,KAAK,QAAQ,EAAE;MACrB,OAAOW,WAAW,CAACE,MAAM,CAACC,OAAO,IAAIA,OAAO,CAACF,UAAU,KAAK,UAAU,CAAC;IACzE;;IAEA;IACA,OAAOD,WAAW;EACpB,CAAC;EAED,MAAMI,iBAAiB,GAAGL,oBAAoB,CAACrC,QAAQ,CAAC;;EAExD;EACA,MAAM2C,kBAAkB,GAAIhB,IAAI,IAAK;IACnC,MAAMiB,SAAS,GAAG,CAChB;MAAEC,KAAK,EAAE,UAAU;MAAEC,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO;IAAE,CAAC,EAC3G;MAAEH,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO;IAAE,CAAC,CACrG;IAED,MAAMC,UAAU,GAAG;MACjB;IAAA,CACD;IAED,MAAMC,UAAU,GAAG,CACjB;MAAEL,KAAK,EAAE,cAAc;MAAEC,IAAI,EAAE,wBAAwB;MAAEC,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE,CAAC,OAAO;IAAE,CAAC,EAC7F;MAAEH,KAAK,EAAE,WAAW;MAAEC,IAAI,EAAE,YAAY;MAAEC,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE,CAAC,OAAO;IAAE,CAAC,EAC/E;MAAEH,KAAK,EAAE,SAAS;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE,YAAY;MAAEC,KAAK,EAAE,CAAC,OAAO;IAAE,CAAC,CAC7E;;IAED;IACA,MAAMG,QAAQ,GAAG,CAAC,GAAGP,SAAS,EAAE,GAAGK,UAAU,EAAE,GAAGC,UAAU,CAAC;IAC7D,OAAOC,QAAQ,CAACX,MAAM,CAACY,IAAI,IAAIA,IAAI,CAACJ,KAAK,CAACK,QAAQ,CAAC1B,IAAI,CAAC2B,WAAW,CAAC,CAAC,CAAC,CAAC;EACzE,CAAC;;EAED;EACA,MAAMC,eAAe,GAAI5B,IAAI,IAAK;IAChC,MAAM6B,QAAQ,GAAG;MACfC,MAAM,EAAE;QACNC,iBAAiB,EAAE,KAAK;QACxBC,gBAAgB,EAAE,KAAK;QACvBC,iBAAiB,EAAE,KAAK;QACxBC,gBAAgB,EAAE,KAAK;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,sBAAsB,EAAE;MAC1B,CAAC;MACDC,MAAM,EAAE;QACNN,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,iBAAiB,EAAE,KAAK;QACxBC,gBAAgB,EAAE,KAAK;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,sBAAsB,EAAE;MAC1B,CAAC;MACDE,KAAK,EAAE;QACLP,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,gBAAgB,EAAE,KAAK;QACvBC,sBAAsB,EAAE;MAC1B,CAAC;MACDG,KAAK,EAAE;QACLR,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QACtBC,gBAAgB,EAAE,IAAI;QACtBC,sBAAsB,EAAE;MAC1B;IACF,CAAC;IACD,OAAOP,QAAQ,CAAC7B,IAAI,CAAC2B,WAAW,CAAC,CAAC,CAAC,IAAIE,QAAQ,CAACC,MAAM;EACxD,CAAC;EAED,MAAMU,eAAe,GAAGxB,kBAAkB,CAAC3C,QAAQ,CAAC;EACpD,MAAMoE,YAAY,GAAGb,eAAe,CAACvD,QAAQ,CAAC;;EAE9C;EACA,MAAM,CAACqE,aAAa,EAAEC,gBAAgB,CAAC,GAAGzF,QAAQ,CAAC,CACjD;IACEmD,EAAE,EAAE,UAAU;IACduC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,mBAAmB;IAC1BC,OAAO,EAAE,gGAAgG;IACzGC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAAE;IAClDC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE;MACJC,MAAM,EAAE,UAAU;MAClBC,SAAS,EAAE,6BAA6B;MACxCC,eAAe,EAAE,6KAA6K;MAC9LC,WAAW,EAAE,qBAAqB;MAClCC,SAAS,EAAE,UAAU;MACrBC,YAAY,EAAE,aAAa;MAC3BC,cAAc,EAAE,6FAA6F;MAC7GC,OAAO,EAAE,IAAIZ,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MACzDY,cAAc,EAAE;IAClB,CAAC;IACDC,OAAO,EAAE,CACP;MAAE5C,KAAK,EAAE,aAAa;MAAE6C,MAAM,EAAE,aAAa;MAAEC,OAAO,EAAE;IAAU,CAAC,EACnE;MAAE9C,KAAK,EAAE,cAAc;MAAE6C,MAAM,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAY,CAAC,EACpE;MAAE9C,KAAK,EAAE,SAAS;MAAE6C,MAAM,EAAE,cAAc;MAAEC,OAAO,EAAE;IAAS,CAAC;EAEnE,CAAC,EACD;IACE3D,EAAE,EAAE,aAAa;IACjBuC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,sBAAsB;IAC7BC,OAAO,EAAE,oFAAoF;IAC7FC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAAE;IACtDC,MAAM,EAAE,KAAK;IACbC,QAAQ,EAAE,QAAQ;IAClBC,IAAI,EAAE;MACJa,SAAS,EAAE,UAAU;MACrBC,YAAY,EAAE,2BAA2B;MACzCC,SAAS,EAAE,eAAe;MAC1BC,eAAe,EAAE,6FAA6F;MAC9GC,SAAS,EAAE,IAAIrB,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MACvDqB,QAAQ,EAAE,GAAG;MAAE;MACfjF,QAAQ,EAAE,0BAA0B;MACpCkF,SAAS,EAAE,CAAC,UAAU,EAAE,aAAa,EAAE,YAAY,CAAC;MACpDC,MAAM,EAAE;IACV,CAAC;IACDV,OAAO,EAAE,CACP;MAAE5C,KAAK,EAAE,QAAQ;MAAE6C,MAAM,EAAE,gBAAgB;MAAEC,OAAO,EAAE;IAAU,CAAC,EACjE;MAAE9C,KAAK,EAAE,SAAS;MAAE6C,MAAM,EAAE,iBAAiB;MAAEC,OAAO,EAAE;IAAY,CAAC,EACrE;MAAE9C,KAAK,EAAE,cAAc;MAAE6C,MAAM,EAAE,cAAc;MAAEC,OAAO,EAAE;IAAY,CAAC;EAE3E,CAAC,EACD;IACE3D,EAAE,EAAE,QAAQ;IACZuC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,wCAAwC;IAC/CC,OAAO,EAAE,kGAAkG;IAC3GC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAAE;IACtDC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,KAAK;IACfC,IAAI,EAAE;MACJqB,cAAc,EAAE,qBAAqB;MACrCC,UAAU,EAAE,IAAI;MAChBC,SAAS,EAAE,yFAAyF;MACpGC,kBAAkB,EAAE,CAClB,sCAAsC,EACtC,sCAAsC,EACtC,4CAA4C,CAC7C;MACDC,cAAc,EAAE;QACdC,UAAU,EAAE,SAAS;QACrBC,cAAc,EAAE,MAAM;QACtBC,aAAa,EAAE;MACjB;IACF,CAAC;IACDlB,OAAO,EAAE,CACP;MAAE5C,KAAK,EAAE,kBAAkB;MAAE6C,MAAM,EAAE,qBAAqB;MAAEC,OAAO,EAAE;IAAU,CAAC,EAChF;MAAE9C,KAAK,EAAE,YAAY;MAAE6C,MAAM,EAAE,iBAAiB;MAAEC,OAAO,EAAE;IAAY,CAAC;EAE5E,CAAC,EACD;IACE3D,EAAE,EAAE,cAAc;IAClBuC,IAAI,EAAE,gBAAgB;IACtBC,KAAK,EAAE,sBAAsB;IAC7BC,OAAO,EAAE,oGAAoG;IAC7GC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAAE;IACtDC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,MAAM;IAChBC,IAAI,EAAE;MACJK,SAAS,EAAE,UAAU;MACrBD,WAAW,EAAE,qBAAqB;MAClCyB,YAAY,EAAE,IAAIjC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;MAAE;MAC9DiC,oBAAoB,EAAE,EAAE;MACxBC,cAAc,EAAE,CAAC;MACjBC,UAAU,EAAE;IACd,CAAC;IACDtB,OAAO,EAAE,CACP;MAAE5C,KAAK,EAAE,cAAc;MAAE6C,MAAM,EAAE,cAAc;MAAEC,OAAO,EAAE;IAAU,CAAC,EACrE;MAAE9C,KAAK,EAAE,eAAe;MAAE6C,MAAM,EAAE,eAAe;MAAEC,OAAO,EAAE;IAAY,CAAC;EAE7E,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAACqB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGpI,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACrE,MAAM,CAACqI,SAAS,EAAEC,YAAY,CAAC,GAAGtI,QAAQ,CAAC,KAAK,CAAC;EAEjD,MAAMuI,WAAW,GAAG/C,aAAa,CAAC7B,MAAM,CAAC6E,CAAC,IAAI,CAACA,CAAC,CAACxC,MAAM,CAAC,CAACyC,MAAM;EAC/D,MAAMC,iBAAiB,GAAGlD,aAAa,CAAC7B,MAAM,CAAC6E,CAAC,IAAIA,CAAC,CAACvC,QAAQ,KAAK,MAAM,IAAI,CAACuC,CAAC,CAACxC,MAAM,CAAC,CAACyC,MAAM;;EAE9F;EACA,MAAME,qBAAqB,GAAGnD,aAAa,CAAC7B,MAAM,CAACiF,YAAY,IAAI;IACjE,QAAQT,kBAAkB;MACxB,KAAK,QAAQ;QACX,OAAO,CAACS,YAAY,CAAC5C,MAAM;MAC7B,KAAK,eAAe;QAClB,OAAO4C,YAAY,CAAC3C,QAAQ,KAAK,MAAM;MACzC;QACE,OAAO,IAAI;IACf;EACF,CAAC,CAAC;EAEF,MAAM4C,UAAU,GAAIC,cAAc,IAAK;IACrCrD,gBAAgB,CAACsD,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACJ,YAAY,IACnBA,YAAY,CAACzF,EAAE,KAAK2F,cAAc,GAC9B;MAAE,GAAGF,YAAY;MAAE5C,MAAM,EAAE;IAAK,CAAC,GACjC4C,YACN,CACF,CAAC;EACH,CAAC;EAED,MAAMK,aAAa,GAAGA,CAAA,KAAM;IAC1BxD,gBAAgB,CAACsD,IAAI,IACnBA,IAAI,CAACC,GAAG,CAACJ,YAAY,KAAK;MAAE,GAAGA,YAAY;MAAE5C,MAAM,EAAE;IAAK,CAAC,CAAC,CAC9D,CAAC;EACH,CAAC;EAED,MAAMkD,mBAAmB,GAAIxD,IAAI,IAAK;IACpC,QAAQA,IAAI;MACV,KAAK,iBAAiB;QAAE,OAAO,UAAU;MACzC,KAAK,gBAAgB;QAAE,OAAO,UAAU;MACxC,KAAK,kBAAkB;QAAE,OAAO,OAAO;MACvC,KAAK,eAAe;QAAE,OAAO,KAAK;MAClC,KAAK,gBAAgB;QAAE,OAAO,eAAe;MAC7C;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,MAAMyD,gBAAgB,GAAIlD,QAAQ,IAAK;IACrC,QAAQA,QAAQ;MACd,KAAK,MAAM;QAAE,OAAO,kBAAkB;MACtC,KAAK,QAAQ;QAAE,OAAO,cAAc;MACpC,KAAK,KAAK;QAAE,OAAO,uBAAuB;MAC1C;QAAS,OAAO,uBAAuB;IACzC;EACF,CAAC;EAED,MAAMmD,eAAe,GAAIvD,SAAS,IAAK;IACrC,MAAME,GAAG,GAAG,IAAID,IAAI,CAAC,CAAC;IACtB,MAAMuD,IAAI,GAAGtD,GAAG,GAAGF,SAAS;IAC5B,MAAMyD,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACH,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;IAC9C,MAAMI,KAAK,GAAGF,IAAI,CAACC,KAAK,CAACH,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACjD,MAAMK,IAAI,GAAGH,IAAI,CAACC,KAAK,CAACH,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAErD,IAAIC,OAAO,GAAG,CAAC,EAAE,OAAO,UAAU;IAClC,IAAIA,OAAO,GAAG,EAAE,EAAE,OAAO,GAAGA,OAAO,OAAO;IAC1C,IAAIG,KAAK,GAAG,EAAE,EAAE,OAAO,GAAGA,KAAK,OAAO;IACtC,IAAIC,IAAI,GAAG,CAAC,EAAE,OAAO,GAAGA,IAAI,OAAO;IACnC,OAAO7D,SAAS,CAAC8D,kBAAkB,CAAC,CAAC;EACvC,CAAC;;EAED;EACA,MAAMC,yBAAyB,GAAGA,CAACpE,aAAa,EAAErE,QAAQ,KAAK;IAC7D,OAAOqE,aAAa,CAAC7B,MAAM,CAACiF,YAAY,IAAI;MAC1C,QAAQA,YAAY,CAAClD,IAAI;QACvB,KAAK,gBAAgB;UACnB;UACA,OAAO,IAAI;QACb,KAAK,eAAe;UAClB;UACA,OAAO,IAAI;QACb,KAAK,iBAAiB;UACpB;UACA;UACA,OAAO,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAClB,QAAQ,CAACrD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsD,WAAW,CAAC,CAAC,CAAC;QACvE,KAAK,gBAAgB;UACnB;UACA,OAAO,IAAI;QACb;UACE,OAAO,IAAI;MACf;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMoF,wBAAwB,GAAG,MAAAA,CAAOf,cAAc,EAAEjC,MAAM,EAAEiD,gBAAgB,KAAK;IACnFxB,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF,QAAQzB,MAAM;QACZ,KAAK,aAAa;UAChB;UACAkD,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,gBAAgB,CAAC3D,MAAM,CAAC;UACvD;UACA/D,QAAQ,CAAC,eAAe,EAAE;YACxB6H,KAAK,EAAE;cACL1D,SAAS,EAAEuD,gBAAgB,CAACvD,SAAS;cACrC2D,eAAe,EAAEJ,gBAAgB,CAAC3D,MAAM;cACxCgE,YAAY,EAAE;YAChB;UACF,CAAC,CAAC;UACF;UACA1E,gBAAgB,CAACsD,IAAI,IAAIA,IAAI,CAACpF,MAAM,CAAC6E,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAK2F,cAAc,CAAC,CAAC;UACnE;QAEF,KAAK,WAAW;UACd;UACA1G,QAAQ,CAAC,eAAe,EAAE;YACxB6H,KAAK,EAAE;cACL1D,SAAS,EAAEuD,gBAAgB,CAACvD,SAAS;cACrC2D,eAAe,EAAEJ,gBAAgB,CAAC3D;YACpC;UACF,CAAC,CAAC;UACF0C,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF,KAAK,cAAc;UACjB;UACA,MAAMsB,MAAM,GAAGC,MAAM,CAAC,6DAA6D,CAAC;UACpFN,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEF,gBAAgB,CAAC3D,MAAM,EAAE,SAAS,EAAEiE,MAAM,CAAC;UAC1E;UACA3E,gBAAgB,CAACsD,IAAI,IAAIA,IAAI,CAACpF,MAAM,CAAC6E,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAK2F,cAAc,CAAC,CAAC;UACnE;QAEF,KAAK,gBAAgB;UACnB;UACAiB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,gBAAgB,CAAC/C,SAAS,CAAC;UAC7D;UACAtB,gBAAgB,CAACsD,IAAI,IAAIA,IAAI,CAACpF,MAAM,CAAC6E,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAK2F,cAAc,CAAC,CAAC;UACnE;QAEF,KAAK,iBAAiB;UACpB;UACAiB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,gBAAgB,CAAC/C,SAAS,CAAC;UAC7DtB,gBAAgB,CAACsD,IAAI,IAAIA,IAAI,CAACpF,MAAM,CAAC6E,CAAC,IAAIA,CAAC,CAACrF,EAAE,KAAK2F,cAAc,CAAC,CAAC;UACnE;QAEF,KAAK,cAAc;UACjB;UACAiB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,gBAAgB,CAAC/C,SAAS,CAAC;UACnE8B,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF,KAAK,cAAc;UACjB;UACAiB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,gBAAgB,CAAC/C,SAAS,CAAC;UAC3DuD,MAAM,CAACC,IAAI,CAAC,gCAAgC,EAAE,QAAQ,CAAC;UACvD1B,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF,KAAK,qBAAqB;UACxB;UACAiB,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEF,gBAAgB,CAACvC,cAAc,CAAC;UACvEsB,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF,KAAK,cAAc;UACjB;UACA1G,QAAQ,CAAC,mBAAmB,EAAE;YAC5B6H,KAAK,EAAE;cACL1D,SAAS,EAAEuD,gBAAgB,CAACvD;YAC9B;UACF,CAAC,CAAC;UACFsC,UAAU,CAACC,cAAc,CAAC;UAC1B;QAEF;UACEiB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEnD,MAAM,CAAC;UACtCgC,UAAU,CAACC,cAAc,CAAC;MAC9B;IACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC7D,CAAC,SAAS;MACRlC,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMmC,YAAY,GAAIxG,IAAI,IAAK9B,QAAQ,CAACuI,QAAQ,KAAKzG,IAAI;;EAEzD;EACA/D,SAAS,CAAC,MAAM;IACd,MAAMyK,kBAAkB,GAAIC,KAAK,IAAK;MACpC,IAAIvI,cAAc,CAACwI,OAAO,IAAI,CAACxI,cAAc,CAACwI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC5EvJ,oBAAoB,CAAC,KAAK,CAAC;MAC7B;MACA,IAAIc,kBAAkB,CAACuI,OAAO,IAAI,CAACvI,kBAAkB,CAACuI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QACpFrJ,wBAAwB,CAAC,KAAK,CAAC;MACjC;MACA,IAAIa,eAAe,CAACsI,OAAO,IAAI,CAACtI,eAAe,CAACsI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC9EnJ,qBAAqB,CAAC,KAAK,CAAC;MAC9B;MACA,IAAIY,aAAa,CAACqI,OAAO,IAAI,CAACrI,aAAa,CAACqI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC1EjJ,mBAAmB,CAAC,KAAK,CAAC;MAC5B;MACA,IAAIW,uBAAuB,CAACoI,OAAO,IAAI,CAACpI,uBAAuB,CAACoI,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAM,CAAC,EAAE;QAC9F/I,6BAA6B,CAAC,KAAK,CAAC;MACtC;IACF,CAAC;IAEDgJ,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAMK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;EAC5E,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,wBAAwB,GAAIC,KAAK,IAAK;IAC1CrB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEoB,KAAK,CAAC;IAChD5J,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAM6J,mBAAmB,GAAIzH,OAAO,IAAK;IACvCmG,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEpG,OAAO,CAAC;IAC7CP,iBAAiB,CAACO,OAAO,CAAC;IAC1BlC,wBAAwB,CAAC,KAAK,CAAC;IAC/B;IACAU,QAAQ,CAAC,mBAAmB,EAAE;MAAE6H,KAAK,EAAE;QAAErG,OAAO,EAAEA;MAAQ;IAAE,CAAC,CAAC;EAChE,CAAC;EAED,MAAM0H,mBAAmB,GAAGA,CAAA,KAAM;IAChCvB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtCtI,wBAAwB,CAAC,KAAK,CAAC;IAC/BQ,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;EAED,MAAMqJ,yBAAyB,GAAG,MAAOC,WAAW,IAAK;IACvD,IAAI;MACFzB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEwB,WAAW,CAAC;MAC7C;MACA;MACA,MAAMC,UAAU,GAAG;QACjBtI,EAAE,EAAE2C,IAAI,CAACC,GAAG,CAAC,CAAC;QACdpD,IAAI,EAAE6I,WAAW,CAAC7I,IAAI;QACtBW,WAAW,EAAEkI,WAAW,CAAClI,WAAW;QACpCC,MAAM,EAAE,QAAQ;QAChBG,UAAU,EAAE;MACd,CAAC;;MAED;MACAL,iBAAiB,CAACoI,UAAU,CAAC;MAC7BvJ,2BAA2B,CAAC,KAAK,CAAC;;MAElC;MACAE,QAAQ,CAAC,mBAAmB,EAAE;QAAE6H,KAAK,EAAE;UAAErG,OAAO,EAAE6H;QAAW;MAAE,CAAC,CAAC;IACnE,CAAC,CAAC,OAAOjB,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMkB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF3B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;;MAE7B;MACA,MAAMpJ,WAAW,CAAC+K,MAAM,CAAC,CAAC;;MAE1B;MACA/J,qBAAqB,CAAC,KAAK,CAAC;MAC5BJ,oBAAoB,CAAC,KAAK,CAAC;MAC3BE,wBAAwB,CAAC,KAAK,CAAC;MAC/BI,mBAAmB,CAAC,KAAK,CAAC;;MAE1B;MACAM,QAAQ,CAAC,QAAQ,EAAE;QAAEwJ,OAAO,EAAE;MAAK,CAAC,CAAC;IACvC,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdT,OAAO,CAACS,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C;MACApI,QAAQ,CAAC,QAAQ,EAAE;QAAEwJ,OAAO,EAAE;MAAK,CAAC,CAAC;IACvC;EACF,CAAC;;EAED;EACA,IAAIzJ,QAAQ,CAACuI,QAAQ,KAAK,QAAQ,IAAIvI,QAAQ,CAACuI,QAAQ,KAAK,WAAW,EAAE;IACvE,OAAO,IAAI;EACb;;EAEA;EACA,MAAMmB,iBAAiB,GAAI/I,IAAI,IAAK;IAClC,MAAMgJ,MAAM,GAAG;MACblH,MAAM,EAAE,2BAA2B;MACnCO,MAAM,EAAE,2BAA2B;MACnCC,KAAK,EAAE,+BAA+B;MACtCC,KAAK,EAAE;IACT,CAAC;IACD,OAAOyG,MAAM,CAAChJ,IAAI,CAAC2B,WAAW,CAAC,CAAC,CAAC,IAAIqH,MAAM,CAAClH,MAAM;EACpD,CAAC;EAED,oBACE7D,OAAA;IAAQgL,SAAS,EAAE;AACvB;AACA,QAAQ5K,QAAQ,KAAK,OAAO,GAClB,gHAAgH,GAChH,iEAAiE;AAC3E,KACM;IAAA6K,QAAA,gBACAjL,OAAA;MAAKgL,SAAS,EAAC,uEAAuE;MAAAC,QAAA,gBAEpFjL,OAAA;QAAKgL,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAChCjL,OAAA,CAACZ,IAAI;UAAC8L,EAAE,EAAC,eAAe;UAACF,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAChHjL,OAAA;YAAKgL,SAAS,EAAE;AAC5B;AACA,gBAAgB5K,QAAQ,KAAK,OAAO,GAClB,+EAA+E,GAC/E,+EAA+E;AACjG,aACc;YAAA6K,QAAA,eACAjL,OAAA;cAAKmL,OAAO,EAAC,WAAW;cAACH,SAAS,EAAC,oBAAoB;cAACI,IAAI,EAAC,cAAc;cAAAH,QAAA,eACzEjL,OAAA;gBAAMqL,CAAC,EAAC;cAA8D;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNzL,OAAA;YAAKgL,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BjL,OAAA;cAAMgL,SAAS,EAAE;AAC/B;AACA,kBAAkB5K,QAAQ,KAAK,OAAO,GAClB,4EAA4E,GAC5E,eAAe;AACnC,eACgB;cAAA6K,QAAA,EAAC;YAEH;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACNrL,QAAQ,KAAK,OAAO,iBACnBJ,OAAA;cAAMgL,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EAAC;YAE5D;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNzL,OAAA;QAAKgL,SAAS,EAAC,uCAAuC;QAAAC,QAAA,GACnDzG,YAAY,CAACL,sBAAsB,gBAClCnE,OAAA;UAAKgL,SAAS,EAAC,UAAU;UAACU,GAAG,EAAEpK,cAAe;UAAA2J,QAAA,gBAC5CjL,OAAA,CAACP,kBAAkB;YACjBsG,OAAO,EAAE3F,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,OAAQ;YAClDuL,OAAO,EAAEA,CAAA,KAAMlL,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;YACxDwK,SAAS,EAAC,qEAAqE;YAAAC,QAAA,gBAE/EjL,OAAA;cAAKgL,SAAS,EAAE;AAChC;AACA,oBAAoB5K,QAAQ,KAAK,OAAO,GAClB,+DAA+D,GAC/D,2BAA2B;AACjD,iBACkB;cAAA6K,QAAA,EACCjJ,YAAY,CAACJ,IAAI,CAACgK,MAAM,CAAC,CAAC;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACNzL,OAAA;cAAKgL,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCjL,OAAA;gBAAMgL,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAEjJ,YAAY,CAACJ;cAAI;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACjErL,QAAQ,KAAK,OAAO,iBACnBJ,OAAA;gBAAMgL,SAAS,EAAC,yBAAyB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACnE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACNzL,OAAA,CAACT,IAAI;cAACqC,IAAI,EAAC,aAAa;cAACiK,IAAI,EAAE,EAAG;cAACb,SAAS,EAAE;AAC9D;AACA,oBAAoBxK,iBAAiB,GAAG,YAAY,GAAG,EAAE;AACzD,oBAAoBJ,QAAQ,KAAK,OAAO,GAAG,iBAAiB,GAAG,eAAe;AAC9E;YAAkB;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACa,CAAC,EAEpBjL,iBAAiB,iBAChBR,OAAA;YAAKgL,SAAS,EAAE;AAChC;AACA;AACA,oBAAoB5K,QAAQ,KAAK,OAAO,GAClB,gEAAgE,GAChE,0BAA0B;AAChD,iBACkB;YAAA6K,QAAA,eACAjL,OAAA;cAAKgL,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjL,OAAA;gBAAKgL,SAAS,EAAE;AACpC;AACA,wBAAwB5K,QAAQ,KAAK,OAAO,GAClB,kCAAkC,GAClC,2BAA2B;AACrD,qBACsB;gBAAA6K,QAAA,EAAC;cAEH;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACNzL,OAAA;gBAAKgL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,EAC5B9I,sBAAsB,CAAC8F,GAAG,CAAE6D,GAAG,iBAC9B9L,OAAA;kBAEE2L,OAAO,EAAEA,CAAA,KAAMvB,wBAAwB,CAAC0B,GAAG,CAAC1J,EAAE,CAAE;kBAChD4I,SAAS,EAAE;AACrC;AACA;AACA,8BAA8B5K,QAAQ,KAAK,OAAO,GAClB,wCAAwC,GACxC,kCAAkC;AAClE,2BAC4B;kBAAA6K,QAAA,gBAEFjL,OAAA;oBAAKgL,SAAS,EAAE;AAC1C;AACA,8BAA8B5K,QAAQ,KAAK,OAAO,GAClB,+DAA+D,GAC/D,2BAA2B;AAC3D,2BAC4B;oBAAA6K,QAAA,EACCa,GAAG,CAAClK,IAAI,CAACgK,MAAM,CAAC,CAAC;kBAAC;oBAAAN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChB,CAAC,eACNzL,OAAA;oBAAKgL,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBjL,OAAA;sBAAKgL,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAEa,GAAG,CAAClK;oBAAI;sBAAA0J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACrEzL,OAAA;sBAAKgL,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEa,GAAG,CAAC7J;oBAAM;sBAAAqJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC,eACNzL,OAAA;oBAAKgL,SAAS,EAAC,6BAA6B;oBAAAC,QAAA,gBAC1CjL,OAAA;sBAAMgL,SAAS,EAAE;AAC7C;AACA,gCAAgCc,GAAG,CAAC/J,IAAI,KAAK,OAAO,GAClB,+BAA+B,GAC/B+J,GAAG,CAAC/J,IAAI,KAAK,OAAO,GACpB,2BAA2B,GAC3B,2BAA2B;AAC7D,6BAC8B;sBAAAkJ,QAAA,EACCa,GAAG,CAAC/J;oBAAI;sBAAAuJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACL,CAAC,EACNK,GAAG,CAAC1J,EAAE,KAAK,CAAC,iBACXpC,OAAA,CAACT,IAAI;sBAACqC,IAAI,EAAC,OAAO;sBAACiK,IAAI,EAAE,EAAG;sBAACb,SAAS,EAAC;oBAAgB;sBAAAM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAC1D;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GAvCDK,GAAG,CAAC1J,EAAE;kBAAAkJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwCL,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;QAAA;QAEN;QACAzL,OAAA;UAAKgL,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDjL,OAAA;YAAKgL,SAAS,EAAC,8DAA8D;YAAAC,QAAA,eAC3EjL,OAAA;cAAMgL,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACpDjJ,YAAY,CAACJ,IAAI,CAACgK,MAAM,CAAC,CAAC;YAAC;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACNzL,OAAA;YAAMgL,SAAS,EAAC,+BAA+B;YAAAC,QAAA,EAAEjJ,YAAY,CAACJ;UAAI;YAAA0J,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CACN,eAGDzL,OAAA;UAAKgL,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EACzC1G,eAAe,CAAC0D,GAAG,CAAEzE,IAAI,IAAK;YAC7B;YACA,IAAIA,IAAI,CAACP,KAAK,KAAK,UAAU,EAAE;cAC7B,oBACEjD,OAAA;gBAAqBgL,SAAS,EAAC,UAAU;gBAACU,GAAG,EAAEnK,kBAAmB;gBAAA0J,QAAA,gBAChEjL,OAAA,CAACP,kBAAkB;kBACjBsG,OAAO,EAAE3F,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,OAAQ;kBACpDuL,OAAO,EAAEA,CAAA,KAAMhL,wBAAwB,CAAC,CAACD,qBAAqB,CAAE;kBAChEsK,SAAS,EAAE;AACjC;AACA;AACA,0BAA0BtB,YAAY,CAAClG,IAAI,CAACN,IAAI,CAAC,GACrB9C,QAAQ,KAAK,OAAO,GAClB,mEAAmE,GACnE,kCAAkC,GACpCA,QAAQ,KAAK,OAAO,GAClB,sDAAsD,GACtD,oDAAoD;AAClF,uBACwB;kBAAA6K,QAAA,gBAEFjL,OAAA,CAACT,IAAI;oBAACqC,IAAI,EAAE4B,IAAI,CAACL,IAAK;oBAAC0I,IAAI,EAAE;kBAAG;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACnCzL,OAAA;oBAAAiL,QAAA,EAAOzH,IAAI,CAACP;kBAAK;oBAAAqI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC,eACzBzL,OAAA,CAACT,IAAI;oBACHqC,IAAI,EAAC,aAAa;oBAClBiK,IAAI,EAAE,EAAG;oBACTb,SAAS,EAAE;AACnC;AACA,4BAA4BtK,qBAAqB,GAAG,YAAY,GAAG,EAAE;AACrE;kBAA0B;oBAAA4K,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACgB,CAAC,EAEpB/K,qBAAqB,iBACpBV,OAAA;kBAAKgL,SAAS,EAAE;AACtC;AACA;AACA,0BAA0B5K,QAAQ,KAAK,OAAO,GAClB,gEAAgE,GAChE,0BAA0B;AACtD,uBACwB;kBAAA6K,QAAA,eACAjL,OAAA;oBAAKgL,SAAS,EAAC,KAAK;oBAAAC,QAAA,gBAClBjL,OAAA;sBAAKgL,SAAS,EAAE;AAC1C;AACA,8BAA8B5K,QAAQ,KAAK,OAAO,GAClB,kCAAkC,GAClC,2BAA2B;AAC3D,2BAC4B;sBAAA6K,QAAA,EAAC;oBAEH;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,EAGL,CAACrL,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,OAAO,kBAC5CJ,OAAA,CAAAE,SAAA;sBAAA+K,QAAA,eACEjL,OAAA;wBACE2L,OAAO,EAAEpB,mBAAoB;wBAC7BS,SAAS,EAAE;AAC3C;AACA;AACA;AACA,oCAAoC5K,QAAQ,KAAK,OAAO,GAClB,0DAA0D,GAC1D,kDAAkD;AACxF,iCACkC;wBAAA6K,QAAA,gBAEFjL,OAAA;0BAAKgL,SAAS,EAAE;AAChD;AACA,oCAAoC5K,QAAQ,KAAK,OAAO,GAClB,0DAA0D,GAC1D,0DAA0D;AAChG;AACA,iCACkC;0BAAA6K,QAAA,eACAjL,OAAA,CAACT,IAAI;4BAACqC,IAAI,EAAC,MAAM;4BAACiK,IAAI,EAAE;0BAAG;4BAAAP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3B,CAAC,eACNzL,OAAA;0BAAKgL,SAAS,EAAC,QAAQ;0BAAAC,QAAA,gBACrBjL,OAAA;4BAAKgL,SAAS,EAAE;AAClD;AACA,sCAAsC5K,QAAQ,KAAK,OAAO,GAAG,iBAAiB,GAAG,eAAe;AAChG,mCAAoC;4BAAA6K,QAAA,EAAC;0BAEH;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC,eACNzL,OAAA;4BAAKgL,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAC;0BAAiC;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAK,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3E,CAAC,eACNzL,OAAA,CAACT,IAAI;0BACHqC,IAAI,EAAC,YAAY;0BACjBiK,IAAI,EAAE,EAAG;0BACTb,SAAS,EAAE;AAC7C;AACA,sCAAsC5K,QAAQ,KAAK,OAAO,GAAG,iBAAiB,GAAG,eAAe;AAChG;wBAAoC;0BAAAkL,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACI;oBAAC,gBACT,CACH,eAGDzL,OAAA;sBAAKgL,SAAS,EAAC,WAAW;sBAAAC,QAAA,EACvBnI,iBAAiB,CAACmF,GAAG,CAAEpF,OAAO,iBAC7B7C,OAAA;wBAEE2L,OAAO,EAAEA,CAAA,KAAMrB,mBAAmB,CAACzH,OAAO,CAAE;wBAC5CmI,SAAS,EAAE;AAC3C;AACA;AACA,oCAAoC5K,QAAQ,KAAK,OAAO,GAClB,wCAAwC,GACxC,kCAAkC;AACxE,iCACkC;wBAAA6K,QAAA,gBAEFjL,OAAA;0BAAKgL,SAAS,EAAE;AAChD;AACA,oCAAoC5K,QAAQ,KAAK,OAAO,GAClB,+DAA+D,GAC/D,2BAA2B;AACjE,iCACkC;0BAAA6K,QAAA,eACAjL,OAAA,CAACT,IAAI;4BAACqC,IAAI,EAAC,QAAQ;4BAACiK,IAAI,EAAE;0BAAG;4BAAAP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7B,CAAC,eACNzL,OAAA;0BAAKgL,SAAS,EAAC,QAAQ;0BAAAC,QAAA,gBACrBjL,OAAA;4BAAKgL,SAAS,EAAC,qCAAqC;4BAAAC,QAAA,EAAEpI,OAAO,CAACjB;0BAAI;4BAAA0J,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC,eACzEzL,OAAA;4BAAKgL,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,EAAEpI,OAAO,CAACN;0BAAW;4BAAA+I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxE,CAAC,eACNzL,OAAA;0BAAKgL,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,gBAChDjL,OAAA;4BAAMgL,SAAS,EAAE;AACnD;AACA,sCAAsCnI,OAAO,CAACL,MAAM,KAAK,QAAQ,GACzB,6BAA6B,GAC7BK,OAAO,CAACL,MAAM,KAAK,UAAU,GAC7B,+BAA+B,GAC/B,2BAA2B;AACnE,mCACoC;4BAAAyI,QAAA,EACCpI,OAAO,CAACL;0BAAM;4BAAA8I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACX,CAAC,EACNrL,QAAQ,KAAK,QAAQ,iBACpBJ,OAAA;4BAAMgL,SAAS,EAAC,uBAAuB;4BAAAC,QAAA,EAAC;0BAAQ;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACvD,EACA5I,OAAO,CAACT,EAAE,KAAKC,cAAc,CAACD,EAAE,iBAC/BpC,OAAA,CAACT,IAAI;4BAACqC,IAAI,EAAC,OAAO;4BAACiK,IAAI,EAAE,EAAG;4BAACb,SAAS,EAAC;0BAAgB;4BAAAM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAC1D;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC;sBAAA,GA1CD5I,OAAO,CAACT,EAAE;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OA2CT,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,GAnJOjI,IAAI,CAACN,IAAI;gBAAAoI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoJd,CAAC;YAEV;;YAEA;YACA,oBACEzL,OAAA,CAACZ,IAAI;cAEH8L,EAAE,EAAE1H,IAAI,CAACN,IAAK;cACd8H,SAAS,EAAE;AAC7B;AACA;AACA,sBAAsBtB,YAAY,CAAClG,IAAI,CAACN,IAAI,CAAC,GACrB9C,QAAQ,KAAK,OAAO,GAClB,mEAAmE,GACnE,kCAAkC,GACpCA,QAAQ,KAAK,OAAO,GAClB,0DAA0D,GAC1D,oDAAoD;AAC9E,mBACoB;cAAA6K,QAAA,gBAEFjL,OAAA,CAACT,IAAI;gBACHqC,IAAI,EAAE4B,IAAI,CAACL,IAAK;gBAChB0I,IAAI,EAAE,EAAG;gBACTb,SAAS,EAAC;cAAyD;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpE,CAAC,eACFzL,OAAA;gBAAAiL,QAAA,EAAOzH,IAAI,CAACP;cAAK;gBAAAqI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA,GApBpBjI,IAAI,CAACN,IAAI;cAAAoI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAqBV,CAAC;UAEX,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNzL,OAAA;QAAKgL,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1CjL,OAAA;UAAKgL,SAAS,EAAC,UAAU;UAACU,GAAG,EAAEhK,uBAAwB;UAAAuJ,QAAA,gBACrDjL,OAAA,CAACP,kBAAkB;YACjBsG,OAAO,EAAC,OAAO;YACf4F,OAAO,EAAEA,CAAA,KAAM1K,6BAA6B,CAAC,CAACD,0BAA0B,CAAE;YAC1EgK,SAAS,EAAE;AACzB;AACA,kBAAkB5K,QAAQ,KAAK,OAAO,GAClB,oCAAoC,GACpC,gCAAgC;AACpD,eACgB;YAAA6K,QAAA,gBAEFjL,OAAA,CAACT,IAAI;cACHqC,IAAI,EAAC,MAAM;cACXiK,IAAI,EAAE,EAAG;cACTb,SAAS,EAAC;YAAyD;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpE,CAAC,EACDjE,WAAW,GAAG,CAAC,iBACdxH,OAAA;cAAMgL,SAAS,EAAE;AACjC;AACA;AACA,oBAAoB5K,QAAQ,KAAK,OAAO,GAClB,yDAAyD,GACzD,uBAAuB;AAC7C,iBACkB;cAAA6K,QAAA,EACCzD,WAAW,GAAG,CAAC,GAAG,IAAI,GAAGA;YAAW;cAAA8D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACiB,CAAC,EAEpBzK,0BAA0B,iBACzBhB,OAAA;YAAKgL,SAAS,EAAC,+GAA+G;YAAAC,QAAA,gBAE5HjL,OAAA;cAAKgL,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCjL,OAAA;gBAAKgL,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDjL,OAAA;kBAAIgL,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,GAAC,eAEhE,EAACzD,WAAW,GAAG,CAAC,iBACdxH,OAAA;oBAAMgL,SAAS,EAAC,gFAAgF;oBAAAC,QAAA,EAC7FzD;kBAAW;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACR,CACP;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EACJjE,WAAW,GAAG,CAAC,iBACdxH,OAAA,CAACR,MAAM;kBACLuG,OAAO,EAAC,OAAO;kBACf8F,IAAI,EAAC,IAAI;kBACTF,OAAO,EAAEzD,aAAc;kBACvB8C,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EACpD;gBAED;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAGNzL,OAAA;gBAAKgL,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BjL,OAAA;kBACE2L,OAAO,EAAEA,CAAA,KAAMtE,qBAAqB,CAAC,KAAK,CAAE;kBAC5C2D,SAAS,EAAE,oDACT5D,kBAAkB,KAAK,KAAK,GACxB,oCAAoC,GACpC,gDAAgD,EACnD;kBAAA6D,QAAA,GACJ,MACK,EAACxG,aAAa,CAACiD,MAAM,GAAG,CAAC,iBAAI1H,OAAA;oBAAMgL,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAExG,aAAa,CAACiD;kBAAM;oBAAA4D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eACTzL,OAAA;kBACE2L,OAAO,EAAEA,CAAA,KAAMtE,qBAAqB,CAAC,QAAQ,CAAE;kBAC/C2D,SAAS,EAAE,oDACT5D,kBAAkB,KAAK,QAAQ,GAC3B,oCAAoC,GACpC,gDAAgD,EACnD;kBAAA6D,QAAA,GACJ,SACQ,EAACzD,WAAW,GAAG,CAAC,iBAAIxH,OAAA;oBAAMgL,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAEzD;kBAAW;oBAAA8D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChE,CAAC,eACTzL,OAAA;kBACE2L,OAAO,EAAEA,CAAA,KAAMtE,qBAAqB,CAAC,eAAe,CAAE;kBACtD2D,SAAS,EAAE,oDACT5D,kBAAkB,KAAK,eAAe,GAClC,oCAAoC,GACpC,gDAAgD,EACnD;kBAAA6D,QAAA,GACJ,gBACe,EAACtD,iBAAiB,GAAG,CAAC,iBAAI3H,OAAA;oBAAMgL,SAAS,EAAC,MAAM;oBAAAC,QAAA,EAAEtD;kBAAiB;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNzL,OAAA;cAAKgL,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EACtCrD,qBAAqB,CAACF,MAAM,KAAK,CAAC,gBACjC1H,OAAA;gBAAKgL,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,gBAClDjL,OAAA,CAACT,IAAI;kBAACqC,IAAI,EAAC,MAAM;kBAACiK,IAAI,EAAE,EAAG;kBAACb,SAAS,EAAC;gBAAyB;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClEzL,OAAA;kBAAGgL,SAAS,EAAC,aAAa;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC/CzL,OAAA;kBAAGgL,SAAS,EAAC,cAAc;kBAAAC,QAAA,EACxB7D,kBAAkB,KAAK,QAAQ,GAAG,gBAAgB,GAClDA,kBAAkB,KAAK,eAAe,GAAG,wBAAwB,GACjE;gBAAiB;kBAAAkE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,GAEN5C,yBAAyB,CAACjB,qBAAqB,EAAExH,QAAQ,CAAC,CAAC6H,GAAG,CAAEJ,YAAY,iBAC1E7H,OAAA;gBAEEgL,SAAS,EAAE,kFACT,CAACnD,YAAY,CAAC5C,MAAM,GAAG,0CAA0C,GAAG,EAAE,EACrE;gBAAAgG,QAAA,eAEHjL,OAAA;kBAAKgL,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBAEzCjL,OAAA;oBAAKgL,SAAS,EAAE,kCACdnD,YAAY,CAAClD,IAAI,KAAK,iBAAiB,GAAG,2BAA2B,GACrEkD,YAAY,CAAClD,IAAI,KAAK,gBAAgB,GAAG,6BAA6B,GACtEkD,YAAY,CAAClD,IAAI,KAAK,kBAAkB,GAAG,+BAA+B,GAC1EkD,YAAY,CAAClD,IAAI,KAAK,eAAe,GAAG,+BAA+B,GACvEkD,YAAY,CAAClD,IAAI,KAAK,gBAAgB,GAAG,yBAAyB,GAClE,2BAA2B,EAC1B;oBAAAsG,QAAA,eACDjL,OAAA,CAACT,IAAI;sBAACqC,IAAI,EAAEuG,mBAAmB,CAACN,YAAY,CAAClD,IAAI,CAAE;sBAACkH,IAAI,EAAE;oBAAG;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eAENzL,OAAA;oBAAKgL,SAAS,EAAC,gBAAgB;oBAAAC,QAAA,gBAE7BjL,OAAA;sBAAKgL,SAAS,EAAC,uCAAuC;sBAAAC,QAAA,gBACpDjL,OAAA;wBAAKgL,SAAS,EAAC,QAAQ;wBAAAC,QAAA,gBACrBjL,OAAA;0BAAKgL,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,gBAC1CjL,OAAA;4BAAGgL,SAAS,EAAE,oBAAoB,CAACnD,YAAY,CAAC5C,MAAM,GAAG,iCAAiC,GAAG,+BAA+B,EAAG;4BAAAgG,QAAA,EAC5HpD,YAAY,CAACjD;0BAAK;4BAAA0G,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClB,CAAC,eACJzL,OAAA;4BAAMgL,SAAS,EAAE,iCAAiC5C,gBAAgB,CAACP,YAAY,CAAC3C,QAAQ,CAAC,WAAY;4BAAA+F,QAAA,EAClGpD,YAAY,CAAC3C;0BAAQ;4BAAAoG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClB,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACJ,CAAC,eACNzL,OAAA;0BAAGgL,SAAS,EAAC,kCAAkC;0BAAAC,QAAA,EAC5C5C,eAAe,CAACR,YAAY,CAAC/C,SAAS;wBAAC;0BAAAwG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,EACL,CAAC5D,YAAY,CAAC5C,MAAM,iBACnBjF,OAAA;wBAAKgL,SAAS,EAAC;sBAAyD;wBAAAM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAC/E;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CAAC,eAGNzL,OAAA;sBAAGgL,SAAS,EAAC,+CAA+C;sBAAAC,QAAA,EACzDpD,YAAY,CAAChD;oBAAO;sBAAAyG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC,EAGH5D,YAAY,CAAClD,IAAI,KAAK,iBAAiB,IAAIkD,YAAY,CAAC1C,IAAI,iBAC3DnF,OAAA;sBAAKgL,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9CjL,OAAA;wBAAKgL,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/CjL,OAAA;0BACE+L,GAAG,EAAElE,YAAY,CAAC1C,IAAI,CAACO,cAAe;0BACtCsG,GAAG,EAAEnE,YAAY,CAAC1C,IAAI,CAACM,YAAa;0BACpCuF,SAAS,EAAC;wBAAsB;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjC,CAAC,eACFzL,OAAA;0BAAMgL,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,GAAC,cAChC,EAACpD,YAAY,CAAC1C,IAAI,CAACM,YAAY;wBAAA;0BAAA6F,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNzL,OAAA;wBAAGgL,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GAAC,WAChC,EAACpD,YAAY,CAAC1C,IAAI,CAACI,WAAW;sBAAA;wBAAA+F,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC,EACH5D,YAAY,CAAC1C,IAAI,CAACQ,OAAO,iBACxB3F,OAAA;wBAAGgL,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GAAC,OACpC,EAACpD,YAAY,CAAC1C,IAAI,CAACQ,OAAO,CAACiD,kBAAkB,CAAC,CAAC;sBAAA;wBAAA0C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnD,CACJ;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE,CACN,EAEA5D,YAAY,CAAClD,IAAI,KAAK,gBAAgB,IAAIkD,YAAY,CAAC1C,IAAI,iBAC1DnF,OAAA;sBAAKgL,SAAS,EAAC,iCAAiC;sBAAAC,QAAA,gBAC9CjL,OAAA;wBAAKgL,SAAS,EAAC,kCAAkC;wBAAAC,QAAA,gBAC/CjL,OAAA;0BACE+L,GAAG,EAAElE,YAAY,CAAC1C,IAAI,CAACgB,eAAgB;0BACvC6F,GAAG,EAAEnE,YAAY,CAAC1C,IAAI,CAACe,SAAU;0BACjC8E,SAAS,EAAC;wBAAsB;0BAAAM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjC,CAAC,eACFzL,OAAA;0BAAMgL,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,GAAC,eAC/B,EAACpD,YAAY,CAAC1C,IAAI,CAACe,SAAS;wBAAA;0BAAAoF,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACJ,CAAC,eACNzL,OAAA;wBAAGgL,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GACvCpD,YAAY,CAAC1C,IAAI,CAACiB,SAAS,CAACwC,kBAAkB,CAAC,CAAC,EAAC,MAAI,EAACf,YAAY,CAAC1C,IAAI,CAACiB,SAAS,CAAC6F,kBAAkB,CAAC,EAAE,EAAE;0BAACC,IAAI,EAAE,SAAS;0BAAEC,MAAM,EAAC;wBAAS,CAAC,CAAC;sBAAA;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7I,CAAC,eACJzL,OAAA;wBAAGgL,SAAS,EAAC,6BAA6B;wBAAAC,QAAA,GAAC,YAC/B,EAACpD,YAAY,CAAC1C,IAAI,CAACkB,QAAQ,EAAC,UACxC;sBAAA;wBAAAiF,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CACN,EAGA5D,YAAY,CAAChC,OAAO,IAAIgC,YAAY,CAAChC,OAAO,CAAC6B,MAAM,GAAG,CAAC,iBACtD1H,OAAA;sBAAKgL,SAAS,EAAC,sBAAsB;sBAAAC,QAAA,EAClCpD,YAAY,CAAChC,OAAO,CAACoC,GAAG,CAAC,CAACnC,MAAM,EAAEsG,KAAK,kBACtCpM,OAAA,CAACR,MAAM;wBAELuG,OAAO,EAAED,MAAM,CAACC,OAAO,KAAK,SAAS,GAAG,SAAS,GAAGD,MAAM,CAACC,OAAO,KAAK,QAAQ,GAAG,aAAa,GAAG,SAAU;wBAC5G8F,IAAI,EAAC,IAAI;wBACTF,OAAO,EAAGU,CAAC,IAAK;0BACdA,CAAC,CAACC,eAAe,CAAC,CAAC;0BACnBxD,wBAAwB,CAACjB,YAAY,CAACzF,EAAE,EAAE0D,MAAM,CAACA,MAAM,EAAE+B,YAAY,CAAC1C,IAAI,CAAC;wBAC7E,CAAE;wBACFoH,QAAQ,EAAEjF,SAAU;wBACpB0D,SAAS,EAAC,aAAa;wBAAAC,QAAA,EAEtBnF,MAAM,CAAC7C;sBAAK,GAVRmJ,KAAK;wBAAAd,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAWJ,CACT;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC,GA9GD5D,YAAY,CAACzF,EAAE;gBAAAkJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA+GjB,CACN;YACF;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGNzL,OAAA;cAAKgL,SAAS,EAAC,4BAA4B;cAAAC,QAAA,eACzCjL,OAAA,CAACR,MAAM;gBACLuG,OAAO,EAAC,OAAO;gBACf8F,IAAI,EAAC,IAAI;gBACTb,SAAS,EAAC,wCAAwC;gBAClDW,OAAO,EAAEA,CAAA,KAAM;kBACb1K,6BAA6B,CAAC,KAAK,CAAC;kBACpC;kBACA+H,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;gBACpD,CAAE;gBAAAgC,QAAA,EACH;cAED;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNzL,OAAA;UAAKgL,SAAS,EAAC,UAAU;UAACU,GAAG,EAAElK,eAAgB;UAAAyJ,QAAA,gBAC7CjL,OAAA,CAACP,kBAAkB;YACjBsG,OAAO,EAAC,OAAO;YACf4F,OAAO,EAAEA,CAAA,KAAM9K,qBAAqB,CAAC,CAACD,kBAAkB,CAAE;YAC1DoK,SAAS,EAAE;AACzB;AACA,kBAAkB5K,QAAQ,KAAK,OAAO,GAClB,gDAAgD,GAChD,kBAAkB;AACtC,eACgB;YAAA6K,QAAA,gBAEFjL,OAAA;cAAKgL,SAAS,EAAE;AAC9B;AACA;AACA,kBAAkB5K,QAAQ,KAAK,OAAO,GAClB,oEAAoE,GACpE,oEAAoE;AACxF,eACgB;cAAA6K,QAAA,EACCtJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,IAAI,GAAGD,IAAI,CAACC,IAAI,CAAC4K,KAAK,CAAC,GAAG,CAAC,CAACvE,GAAG,CAACR,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACgF,IAAI,CAAC,EAAE,CAAC,GAAG;YAAG;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC,eACNzL,OAAA;cAAKgL,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxCjL,OAAA;gBAAKgL,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAE,CAAAtJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,KAAI;cAAM;gBAAA0J,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACjFzL,OAAA;gBAAKgL,SAAS,EAAE;AAChC;AACA,oBAAoB5K,QAAQ,KAAK,OAAO,GAAG,iBAAiB,GAAG,eAAe;AAC9E,iBAAkB;gBAAA6K,QAAA,EACC7K,QAAQ,KAAK,OAAO,GAAG,oBAAoB,GAAGA,QAAQ,CAACwL,MAAM,CAAC,CAAC,CAAC,CAACc,WAAW,CAAC,CAAC,GAAGtM,QAAQ,CAACuM,KAAK,CAAC,CAAC;cAAC;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzL,OAAA,CAACT,IAAI;cACHqC,IAAI,EAAC,aAAa;cAClBiK,IAAI,EAAE,EAAG;cACTb,SAAS,EAAE;AAC3B;AACA,oBAAoBpK,kBAAkB,GAAG,YAAY,GAAG,EAAE;AAC1D,oBAAoBR,QAAQ,KAAK,OAAO,GAAG,iBAAiB,GAAG,eAAe;AAC9E;YAAkB;cAAAkL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACgB,CAAC,EAEpB7K,kBAAkB,iBACjBZ,OAAA;YAAKgL,SAAS,EAAE;AAC9B;AACA;AACA,kBAAkB5K,QAAQ,KAAK,OAAO,GAClB,gEAAgE,GAChE,0BAA0B;AAC9C,eACgB;YAAA6K,QAAA,eACAjL,OAAA;cAAKgL,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBjL,OAAA;gBAAKgL,SAAS,EAAE;AAClC;AACA,sBAAsB5K,QAAQ,KAAK,OAAO,GAClB,6EAA6E,GAC7E,mCAAmC;AAC3D,mBACoB;gBAAA6K,QAAA,eACAjL,OAAA;kBAAKgL,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC1CjL,OAAA;oBAAKgL,SAAS,EAAE;AACtC;AACA,0BAA0B5K,QAAQ,KAAK,OAAO,GAClB,0DAA0D,GAC1D,0DAA0D;AACtF,uBACwB;oBAAA6K,QAAA,EACCtJ,IAAI,aAAJA,IAAI,eAAJA,IAAI,CAAEC,IAAI,GAAGD,IAAI,CAACC,IAAI,CAAC4K,KAAK,CAAC,GAAG,CAAC,CAACvE,GAAG,CAACR,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC,CAAC,CAACgF,IAAI,CAAC,EAAE,CAAC,GAAG;kBAAG;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7D,CAAC,eACNzL,OAAA;oBAAKgL,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBjL,OAAA;sBAAKgL,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAE,CAAAtJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEC,IAAI,KAAI;oBAAM;sBAAA0J,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACzEzL,OAAA;sBAAKgL,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAE,CAAAtJ,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEE,KAAK,KAAI;oBAAkB;sBAAAyJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChFzL,OAAA;sBAAMgL,SAAS,EAAE;AACzC;AACA,4BAA4B5K,QAAQ,KAAK,OAAO,GAClB,yDAAyD,GACzDA,QAAQ,KAAK,OAAO,GACpB,2BAA2B,GAC3B,2BAA2B;AACzD,yBAC0B;sBAAA6K,QAAA,EACC7K,QAAQ,KAAK,OAAO,GAAG,oBAAoB,GAAGA,QAAQ,CAACwL,MAAM,CAAC,CAAC,CAAC,CAACc,WAAW,CAAC,CAAC,GAAGtM,QAAQ,CAACuM,KAAK,CAAC,CAAC;oBAAC;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/F,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzL,OAAA;gBAAKgL,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBjL,OAAA,CAACZ,IAAI;kBACH8L,EAAE,EAAC,wBAAwB;kBAC3BF,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBAE7HjL,OAAA,CAACT,IAAI;oBAACqC,IAAI,EAAC,MAAM;oBAACiK,IAAI,EAAE;kBAAG;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9BzL,OAAA;oBAAAiL,QAAA,EAAM;kBAAgB;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzB,CAAC,eACPzL,OAAA;kBAAQgL,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBACnIjL,OAAA,CAACT,IAAI;oBAACqC,IAAI,EAAC,MAAM;oBAACiK,IAAI,EAAE;kBAAG;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC9BzL,OAAA;oBAAAiL,QAAA,EAAM;kBAAa;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC,eACTzL,OAAA;kBAAQgL,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,gBACnIjL,OAAA,CAACT,IAAI;oBAACqC,IAAI,EAAC,YAAY;oBAACiK,IAAI,EAAE;kBAAG;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpCzL,OAAA;oBAAAiL,QAAA,EAAM;kBAAc;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrB,CAAC,eACTzL,OAAA;kBAAKgL,SAAS,EAAC;gBAA6B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnDzL,OAAA;kBACE2L,OAAO,EAAEhB,YAAa;kBACtBK,SAAS,EAAC,kHAAkH;kBAAAC,QAAA,gBAE5HjL,OAAA,CAACT,IAAI;oBAACqC,IAAI,EAAC,QAAQ;oBAACiK,IAAI,EAAE;kBAAG;oBAAAP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAChCzL,OAAA;oBAAAiL,QAAA,EAAM;kBAAQ;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNzL,OAAA,CAACR,MAAM;UACLuG,OAAO,EAAC,OAAO;UACf8F,IAAI,EAAC,MAAM;UACXF,OAAO,EAAEA,CAAA,KAAM5K,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;UACtDkK,SAAS,EAAC,WAAW;UAAAC,QAAA,eAErBjL,OAAA,CAACT,IAAI;YAACqC,IAAI,EAAEd,gBAAgB,GAAG,GAAG,GAAG,MAAO;YAAC+K,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL3K,gBAAgB,iBACfd,OAAA;MAAK0L,GAAG,EAAEjK,aAAc;MAACuJ,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC9EjL,OAAA;QAAKgL,SAAS,EAAC,qBAAqB;QAAAC,QAAA,GAEjCzG,YAAY,CAACV,iBAAiB,iBAC7B9D,OAAA;UACE2L,OAAO,EAAEA,CAAA,KAAM;YACbtK,QAAQ,CAAC,qBAAqB,CAAC;YAC/BN,mBAAmB,CAAC,KAAK,CAAC;UAC5B,CAAE;UACFiK,SAAS,EAAC,qJAAqJ;UAAAC,QAAA,gBAE/JjL,OAAA,CAACT,IAAI;YAACqC,IAAI,EAAC,MAAM;YAACiK,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC9BzL,OAAA;YAAAiL,QAAA,EAAM;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CACT,EAEAlH,eAAe,CAAC0D,GAAG,CAAEzE,IAAI,iBACxBxD,OAAA,CAACZ,IAAI;UAEH8L,EAAE,EAAE1H,IAAI,CAACN,IAAK;UACdyI,OAAO,EAAEA,CAAA,KAAM5K,mBAAmB,CAAC,KAAK,CAAE;UAC1CiK,SAAS,EAAE,yFACTtB,YAAY,CAAClG,IAAI,CAACN,IAAI,CAAC,GACnB,oCAAoC,GACpC,4DAA4D,EAC/D;UAAA+H,QAAA,gBAEHjL,OAAA,CAACT,IAAI;YAACqC,IAAI,EAAE4B,IAAI,CAACL,IAAK;YAAC0I,IAAI,EAAE;UAAG;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCzL,OAAA;YAAAiL,QAAA,EAAOzH,IAAI,CAACP;UAAK;YAAAqI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAVpBjI,IAAI,CAACN,IAAI;UAAAoI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWV,CACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGDzL,OAAA,CAACF,kBAAkB;MACjB8M,MAAM,EAAE1L,wBAAyB;MACjC2L,OAAO,EAAEA,CAAA,KAAM1L,2BAA2B,CAAC,KAAK,CAAE;MAClD2L,eAAe,EAAEtC,yBAA0B;MAC3CuC,cAAc,EAAE/K,YAAY,CAACI,EAAE,IAAI,CAAE;MACrC4K,gBAAgB,EAAEhL,YAAY,CAACJ;IAAK;MAAA0J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;AAAClL,EAAA,CAzwCIJ,eAAe;EAAA,QAOFd,WAAW,EACXC,WAAW;AAAA;AAAA2N,EAAA,GARxB9M,eAAe;AA2wCrB,eAAeA,eAAe;AAAC,IAAA8M,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}