#!/usr/bin/env python3
"""
Enhanced FastAPI server with RBAC, email notifications, and organization management
"""
import sys
import os
import time
import uuid
from typing import Optional, List
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException, Header, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
from app.config import settings
from app.core.security import hash_password, verify_password, create_access_token, verify_token
from app.services.email_service import email_service

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Enhanced Agno WorkSphere API with RBAC and email notifications"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for testing (replace with database in production)
users_db = {}
organizations_db = {}
organization_members_db = {}
projects_db = {}
invitations_db = {}

# Pydantic models
class UserRegister(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    organization_name: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int = 900

class UserResponse(BaseModel):
    id: str
    email: str
    first_name: str
    last_name: str
    email_verified: bool = True
    created_at: float

class OrganizationResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    created_at: float
    member_count: int = 1

class OrganizationMemberResponse(BaseModel):
    id: str
    user_id: str
    organization_id: str
    role: str
    joined_at: float
    user: UserResponse

class AuthResponse(BaseModel):
    user: UserResponse
    tokens: TokenResponse
    organization: Optional[OrganizationResponse] = None
    role: Optional[str] = None

class OrganizationCreate(BaseModel):
    name: str
    description: Optional[str] = None
    allowed_domains: Optional[List[str]] = None

class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None
    organization_id: str

class InviteMember(BaseModel):
    email: EmailStr
    role: str = "member"  # viewer, member, admin

class DashboardStats(BaseModel):
    total_organizations: int
    total_projects: int
    total_members: int
    recent_activity: List[dict]


def get_user_from_token(authorization: Optional[str] = Header(None)):
    """Get user from authorization token"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    try:
        token = authorization.replace("Bearer ", "")
        payload = verify_token(token)
        user_id = payload.get("sub")
        
        # Find user in database
        for email, user in users_db.items():
            if user["id"] == user_id:
                return user
        
        raise HTTPException(status_code=401, detail="User not found")
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid token")


def check_organization_access(user_id: str, organization_id: str, required_roles: List[str] = None):
    """Check if user has access to organization with required role"""
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["organization_id"] == organization_id:
            if required_roles and member["role"] not in required_roles:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
            return member
    
    raise HTTPException(status_code=403, detail="Access denied to organization")


def check_domain_access(user_email: str, organization_id: str):
    """Check if user's email domain is allowed for organization"""
    org = organizations_db.get(organization_id)
    if not org or not org.get("allowed_domains"):
        return True  # No domain restrictions
    
    user_domain = user_email.split("@")[1].lower()
    allowed_domains = [domain.lower() for domain in org["allowed_domains"]]
    
    return user_domain in allowed_domains


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "success": True,
        "data": {
            "message": "Welcome to Agno WorkSphere API (Enhanced)",
            "version": settings.app_version,
            "environment": settings.environment,
            "features": ["RBAC", "Email Notifications", "Domain Restrictions"]
        },
        "timestamp": time.time()
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "version": settings.app_version,
            "environment": settings.environment,
            "mode": "enhanced",
            "email_configured": bool(settings.smtp_user and settings.smtp_pass)
        },
        "timestamp": time.time()
    }


@app.post("/api/v1/auth/register", response_model=dict)
async def register(
    user_data: UserRegister,
    background_tasks: BackgroundTasks
):
    """Register a new user and create organization"""
    # Check if user already exists
    if user_data.email in users_db:
        raise HTTPException(status_code=409, detail="User with this email already exists")
    
    # Create user
    user_id = str(uuid.uuid4())
    hashed_password = hash_password(user_data.password)
    
    user = {
        "id": user_id,
        "email": user_data.email,
        "password_hash": hashed_password,
        "first_name": user_data.first_name,
        "last_name": user_data.last_name,
        "email_verified": True,  # Auto-verify for demo
        "created_at": time.time()
    }
    
    users_db[user_data.email] = user
    
    # Create default organization for the user
    org_name = user_data.organization_name or f"{user_data.first_name}'s Organization"
    org_id = str(uuid.uuid4())
    
    organization = {
        "id": org_id,
        "name": org_name,
        "description": f"Default organization for {user_data.first_name} {user_data.last_name}",
        "created_by": user_id,
        "created_at": time.time(),
        "allowed_domains": [user_data.email.split("@")[1]]  # Allow same domain by default
    }
    
    organizations_db[org_id] = organization
    
    # Add user as organization owner
    member_id = str(uuid.uuid4())
    member = {
        "id": member_id,
        "user_id": user_id,
        "organization_id": org_id,
        "role": "owner",
        "joined_at": time.time(),
        "invited_by": None
    }
    
    organization_members_db[member_id] = member
    
    # Generate token
    access_token = create_access_token({
        "sub": user_id,
        "email": user_data.email,
        "role": "owner",
        "org_id": org_id
    })
    
    # Send welcome email in background
    background_tasks.add_task(
        email_service.send_welcome_email,
        user_data.email,
        f"{user_data.first_name} {user_data.last_name}",
        org_name
    )
    
    return {
        "success": True,
        "data": AuthResponse(
            user=UserResponse(**user),
            tokens=TokenResponse(access_token=access_token),
            organization=OrganizationResponse(
                **organization,
                member_count=1
            ),
            role="owner"
        ),
        "message": "Registration successful! Welcome email sent."
    }


@app.post("/api/v1/auth/login", response_model=dict)
async def login(user_data: UserLogin):
    """Login user"""
    # Find user
    user = users_db.get(user_data.email)
    if not user or not verify_password(user_data.password, user["password_hash"]):
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    # Get user's primary organization and role
    primary_org = None
    user_role = None
    
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user["id"]:
            org = organizations_db.get(member["organization_id"])
            if org:
                primary_org = org
                user_role = member["role"]
                break
    
    # Generate token
    token_data = {
        "sub": user["id"],
        "email": user["email"]
    }
    
    if primary_org:
        token_data.update({
            "role": user_role,
            "org_id": primary_org["id"]
        })
    
    access_token = create_access_token(token_data)
    
    response_data = AuthResponse(
        user=UserResponse(**user),
        tokens=TokenResponse(access_token=access_token),
        role=user_role
    )
    
    if primary_org:
        # Count members
        member_count = sum(1 for m in organization_members_db.values() 
                          if m["organization_id"] == primary_org["id"])
        
        response_data.organization = OrganizationResponse(
            **primary_org,
            member_count=member_count
        )
    
    return {
        "success": True,
        "data": response_data,
        "message": "Login successful"
    }


@app.get("/api/v1/dashboard/stats")
async def get_dashboard_stats(current_user: dict = Depends(get_user_from_token)):
    """Get dashboard statistics based on user role"""
    user_id = current_user["id"]

    # Get user's organizations
    user_orgs = []
    user_role_in_primary_org = None

    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                user_orgs.append({
                    "organization": org,
                    "role": member["role"]
                })
                if not user_role_in_primary_org:
                    user_role_in_primary_org = member["role"]

    # Count projects user has access to
    accessible_projects = []
    for project_id, project in projects_db.items():
        # Check if user has access to project's organization
        for org_info in user_orgs:
            if project["organization_id"] == org_info["organization"]["id"]:
                accessible_projects.append(project)
                break

    # Count total members across user's organizations
    total_members = 0
    for org_info in user_orgs:
        org_id = org_info["organization"]["id"]
        org_member_count = sum(1 for m in organization_members_db.values()
                              if m["organization_id"] == org_id)
        total_members += org_member_count

    # Recent activity (mock data for now)
    recent_activity = [
        {
            "type": "project_created",
            "message": "New project created",
            "timestamp": time.time() - 3600,
            "user": f"{current_user['first_name']} {current_user['last_name']}"
        },
        {
            "type": "member_joined",
            "message": "New member joined organization",
            "timestamp": time.time() - 7200,
            "user": "System"
        }
    ]

    return {
        "success": True,
        "data": DashboardStats(
            total_organizations=len(user_orgs),
            total_projects=len(accessible_projects),
            total_members=total_members,
            recent_activity=recent_activity
        ),
        "user_role": user_role_in_primary_org
    }


@app.get("/api/v1/users/profile")
async def get_profile(current_user: dict = Depends(get_user_from_token)):
    """Get current user profile with organization info"""
    user_id = current_user["id"]

    # Get user's organizations and roles
    organizations = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                member_count = sum(1 for m in organization_members_db.values()
                                 if m["organization_id"] == org["id"])

                organizations.append({
                    "organization": OrganizationResponse(**org, member_count=member_count),
                    "role": member["role"],
                    "joined_at": member["joined_at"]
                })

    return {
        "success": True,
        "data": {
            "user": UserResponse(**current_user),
            "organizations": organizations
        }
    }


@app.get("/api/v1/organizations")
async def get_organizations(current_user: dict = Depends(get_user_from_token)):
    """Get organizations user has access to"""
    user_id = current_user["id"]
    user_email = current_user["email"]

    organizations = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                # Check domain access
                if check_domain_access(user_email, org["id"]):
                    member_count = sum(1 for m in organization_members_db.values()
                                     if m["organization_id"] == org["id"])

                    organizations.append({
                        "organization": OrganizationResponse(**org, member_count=member_count),
                        "role": member["role"]
                    })

    return {
        "success": True,
        "data": organizations
    }


@app.post("/api/v1/organizations")
async def create_organization(
    org_data: OrganizationCreate,
    current_user: dict = Depends(get_user_from_token)
):
    """Create a new organization (owner/admin only)"""
    user_id = current_user["id"]

    # Check if user has permission to create organizations
    # For now, allow any authenticated user to create organizations

    org_id = str(uuid.uuid4())
    organization = {
        "id": org_id,
        "name": org_data.name,
        "description": org_data.description,
        "created_by": user_id,
        "created_at": time.time(),
        "allowed_domains": org_data.allowed_domains or []
    }

    organizations_db[org_id] = organization

    # Add creator as owner
    member_id = str(uuid.uuid4())
    member = {
        "id": member_id,
        "user_id": user_id,
        "organization_id": org_id,
        "role": "owner",
        "joined_at": time.time(),
        "invited_by": None
    }

    organization_members_db[member_id] = member

    return {
        "success": True,
        "data": {
            "organization": OrganizationResponse(**organization, member_count=1),
            "role": "owner"
        },
        "message": "Organization created successfully"
    }


@app.get("/api/v1/organizations/{org_id}/members")
async def get_organization_members(
    org_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get organization members (member+ role required)"""
    user_id = current_user["id"]

    # Check access
    check_organization_access(user_id, org_id, ["member", "admin", "owner"])

    members = []
    for member_id, member in organization_members_db.items():
        if member["organization_id"] == org_id:
            # Get user info
            user_info = None
            for email, user in users_db.items():
                if user["id"] == member["user_id"]:
                    user_info = user
                    break

            if user_info:
                members.append(OrganizationMemberResponse(
                    **member,
                    user=UserResponse(**user_info)
                ))

    return {
        "success": True,
        "data": members
    }


@app.post("/api/v1/organizations/{org_id}/invite")
async def invite_member(
    org_id: str,
    invite_data: InviteMember,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_user_from_token)
):
    """Invite member to organization (admin+ role required)"""
    user_id = current_user["id"]

    # Check access
    member_info = check_organization_access(user_id, org_id, ["admin", "owner"])

    # Check domain restrictions
    if not check_domain_access(invite_data.email, org_id):
        raise HTTPException(
            status_code=403,
            detail="Email domain not allowed for this organization"
        )

    # Check if user already exists
    existing_user = users_db.get(invite_data.email)

    if existing_user:
        # Check if already a member
        for member_id, member in organization_members_db.items():
            if (member["user_id"] == existing_user["id"] and
                member["organization_id"] == org_id):
                raise HTTPException(status_code=409, detail="User is already a member")

        # Add existing user to organization
        member_id = str(uuid.uuid4())
        member = {
            "id": member_id,
            "user_id": existing_user["id"],
            "organization_id": org_id,
            "role": invite_data.role,
            "joined_at": time.time(),
            "invited_by": user_id
        }

        organization_members_db[member_id] = member

        message = "User added to organization successfully"
    else:
        # Create invitation for new user
        invitation_id = str(uuid.uuid4())
        invitation = {
            "id": invitation_id,
            "email": invite_data.email,
            "organization_id": org_id,
            "role": invite_data.role,
            "invited_by": user_id,
            "created_at": time.time(),
            "expires_at": time.time() + (7 * 24 * 3600)  # 7 days
        }

        invitations_db[invitation_id] = invitation

        # Send invitation email
        org = organizations_db[org_id]
        inviter_name = f"{current_user['first_name']} {current_user['last_name']}"
        invitation_url = f"http://localhost:3000/invite/{invitation_id}"

        background_tasks.add_task(
            email_service.send_invitation_email,
            invite_data.email,
            inviter_name,
            org["name"],
            invite_data.role,
            invitation_url
        )

        message = "Invitation sent successfully"

    return {
        "success": True,
        "message": message
    }


@app.get("/api/v1/projects")
async def get_projects(
    organization_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get projects user has access to"""
    user_id = current_user["id"]

    accessible_projects = []

    if organization_id:
        # Check access to specific organization
        check_organization_access(user_id, organization_id)

        # Get projects for this organization
        for project_id, project in projects_db.items():
            if project["organization_id"] == organization_id:
                accessible_projects.append(project)
    else:
        # Get all projects user has access to
        user_org_ids = []
        for member_id, member in organization_members_db.items():
            if member["user_id"] == user_id:
                user_org_ids.append(member["organization_id"])

        for project_id, project in projects_db.items():
            if project["organization_id"] in user_org_ids:
                accessible_projects.append(project)

    return {
        "success": True,
        "data": accessible_projects
    }


@app.post("/api/v1/projects")
async def create_project(
    project_data: ProjectCreate,
    current_user: dict = Depends(get_user_from_token)
):
    """Create project (member+ role required)"""
    user_id = current_user["id"]

    # Check access to organization
    check_organization_access(user_id, project_data.organization_id, ["member", "admin", "owner"])

    project_id = str(uuid.uuid4())
    project = {
        "id": project_id,
        "name": project_data.name,
        "description": project_data.description,
        "organization_id": project_data.organization_id,
        "status": "active",
        "created_by": user_id,
        "created_at": time.time()
    }

    projects_db[project_id] = project

    return {
        "success": True,
        "data": project,
        "message": "Project created successfully"
    }


# Additional endpoints for complete API coverage

@app.put("/api/v1/users/profile")
async def update_profile(
    profile_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update user profile"""
    user_id = current_user["id"]
    user_email = current_user["email"]

    if user_email in users_db:
        user = users_db[user_email]
        # Update allowed fields
        if "first_name" in profile_data:
            user["first_name"] = profile_data["first_name"]
        if "last_name" in profile_data:
            user["last_name"] = profile_data["last_name"]
        if "bio" in profile_data:
            user["bio"] = profile_data["bio"]

        users_db[user_email] = user

        return {
            "success": True,
            "data": {
                "id": user["id"],
                "email": user["email"],
                "first_name": user["first_name"],
                "last_name": user["last_name"],
                "bio": user.get("bio", "")
            },
            "message": "Profile updated successfully"
        }

    raise HTTPException(status_code=404, detail="User not found")


@app.get("/api/v1/organizations/{org_id}")
async def get_organization(
    org_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get organization details"""
    user_id = current_user["id"]

    # Check if user has access to this organization
    has_access = False
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["organization_id"] == org_id:
            has_access = True
            break

    if not has_access:
        raise HTTPException(status_code=404, detail="Organization not found")

    if org_id not in organizations_db:
        raise HTTPException(status_code=404, detail="Organization not found")

    org = organizations_db[org_id]
    return {
        "success": True,
        "data": org
    }


@app.get("/api/v1/projects/{project_id}")
async def get_project(
    project_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get project details"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    # Check access to organization
    try:
        check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
    except HTTPException:
        raise HTTPException(status_code=404, detail="Project not found")

    return {
        "success": True,
        "data": project
    }


@app.put("/api/v1/projects/{project_id}")
async def update_project(
    project_id: str,
    project_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update project"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    # Check access (admin/owner only for updates)
    check_organization_access(user_id, project["organization_id"], ["admin", "owner"])

    # Update allowed fields
    if "name" in project_data:
        project["name"] = project_data["name"]
    if "description" in project_data:
        project["description"] = project_data["description"]
    if "status" in project_data:
        project["status"] = project_data["status"]

    projects_db[project_id] = project

    return {
        "success": True,
        "data": project,
        "message": "Project updated successfully"
    }


# Board Management Endpoints
boards_db = {}
columns_db = {}
cards_db = {}

@app.get("/api/v1/boards")
async def get_boards(
    project_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get boards"""
    user_id = current_user["id"]
    accessible_boards = []

    for board_id, board in boards_db.items():
        if project_id and board["project_id"] != project_id:
            continue

        # Check access to project's organization
        if board["project_id"] in projects_db:
            project = projects_db[board["project_id"]]
            try:
                check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
                accessible_boards.append(board)
            except HTTPException:
                continue

    return {
        "success": True,
        "data": accessible_boards
    }


@app.post("/api/v1/boards")
async def create_board(
    board_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create board"""
    user_id = current_user["id"]
    project_id = board_data.get("project_id")

    if not project_id or project_id not in projects_db:
        raise HTTPException(status_code=400, detail="Valid project_id required")

    project = projects_db[project_id]
    check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    board_id = str(uuid.uuid4())
    board = {
        "id": board_id,
        "name": board_data.get("name", "New Board"),
        "description": board_data.get("description", ""),
        "project_id": project_id,
        "created_by": user_id,
        "created_at": time.time()
    }

    boards_db[board_id] = board

    return {
        "success": True,
        "data": board,
        "message": "Board created successfully"
    }


@app.get("/api/v1/boards/{board_id}")
async def get_board(
    board_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get board details"""
    if board_id not in boards_db:
        raise HTTPException(status_code=404, detail="Board not found")

    board = boards_db[board_id]
    user_id = current_user["id"]

    # Check access
    if board["project_id"] in projects_db:
        project = projects_db[board["project_id"]]
        check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])

    return {
        "success": True,
        "data": board
    }


# Column Management Endpoints
@app.get("/api/v1/columns")
async def get_columns(
    board_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get columns"""
    user_id = current_user["id"]
    accessible_columns = []

    for column_id, column in columns_db.items():
        if board_id and column["board_id"] != board_id:
            continue

        # Check access through board -> project -> organization
        if column["board_id"] in boards_db:
            board = boards_db[column["board_id"]]
            if board["project_id"] in projects_db:
                project = projects_db[board["project_id"]]
                try:
                    check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
                    accessible_columns.append(column)
                except HTTPException:
                    continue

    return {
        "success": True,
        "data": accessible_columns
    }


@app.post("/api/v1/columns")
async def create_column(
    column_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create column"""
    user_id = current_user["id"]
    board_id = column_data.get("board_id")

    if not board_id or board_id not in boards_db:
        raise HTTPException(status_code=400, detail="Valid board_id required")

    board = boards_db[board_id]
    if board["project_id"] in projects_db:
        project = projects_db[board["project_id"]]
        check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    column_id = str(uuid.uuid4())
    column = {
        "id": column_id,
        "name": column_data.get("name", "New Column"),
        "board_id": board_id,
        "position": column_data.get("position", 0),
        "created_by": user_id,
        "created_at": time.time()
    }

    columns_db[column_id] = column

    return {
        "success": True,
        "data": column,
        "message": "Column created successfully"
    }


@app.put("/api/v1/columns/{column_id}")
async def update_column(
    column_id: str,
    column_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update column"""
    if column_id not in columns_db:
        raise HTTPException(status_code=404, detail="Column not found")

    column = columns_db[column_id]
    user_id = current_user["id"]

    # Check access
    if column["board_id"] in boards_db:
        board = boards_db[column["board_id"]]
        if board["project_id"] in projects_db:
            project = projects_db[board["project_id"]]
            check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    # Update fields
    if "name" in column_data:
        column["name"] = column_data["name"]
    if "position" in column_data:
        column["position"] = column_data["position"]

    columns_db[column_id] = column

    return {
        "success": True,
        "data": column,
        "message": "Column updated successfully"
    }


@app.delete("/api/v1/columns/{column_id}")
async def delete_column(
    column_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Delete column"""
    if column_id not in columns_db:
        raise HTTPException(status_code=404, detail="Column not found")

    column = columns_db[column_id]
    user_id = current_user["id"]

    # Check access
    if column["board_id"] in boards_db:
        board = boards_db[column["board_id"]]
        if board["project_id"] in projects_db:
            project = projects_db[board["project_id"]]
            check_organization_access(user_id, project["organization_id"], ["admin", "owner"])

    del columns_db[column_id]

    return {
        "success": True,
        "message": "Column deleted successfully"
    }


# Card Management Endpoints
@app.get("/api/v1/cards")
async def get_cards(
    column_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get cards"""
    user_id = current_user["id"]
    accessible_cards = []

    for card_id, card in cards_db.items():
        if column_id and card["column_id"] != column_id:
            continue

        # Check access through column -> board -> project -> organization
        if card["column_id"] in columns_db:
            column = columns_db[card["column_id"]]
            if column["board_id"] in boards_db:
                board = boards_db[column["board_id"]]
                if board["project_id"] in projects_db:
                    project = projects_db[board["project_id"]]
                    try:
                        check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])
                        accessible_cards.append(card)
                    except HTTPException:
                        continue

    return {
        "success": True,
        "data": accessible_cards
    }


@app.post("/api/v1/cards")
async def create_card(
    card_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create card"""
    user_id = current_user["id"]
    column_id = card_data.get("column_id")

    if not column_id or column_id not in columns_db:
        raise HTTPException(status_code=400, detail="Valid column_id required")

    # Check access
    column = columns_db[column_id]
    if column["board_id"] in boards_db:
        board = boards_db[column["board_id"]]
        if board["project_id"] in projects_db:
            project = projects_db[board["project_id"]]
            check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    card_id = str(uuid.uuid4())
    card = {
        "id": card_id,
        "title": card_data.get("title", "New Card"),
        "description": card_data.get("description", ""),
        "column_id": column_id,
        "position": card_data.get("position", 0),
        "priority": card_data.get("priority", "medium"),
        "created_by": user_id,
        "created_at": time.time()
    }

    cards_db[card_id] = card

    return {
        "success": True,
        "data": card,
        "message": "Card created successfully"
    }


@app.put("/api/v1/cards/{card_id}")
async def update_card(
    card_id: str,
    card_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update card"""
    if card_id not in cards_db:
        raise HTTPException(status_code=404, detail="Card not found")

    card = cards_db[card_id]
    user_id = current_user["id"]

    # Check access
    if card["column_id"] in columns_db:
        column = columns_db[card["column_id"]]
        if column["board_id"] in boards_db:
            board = boards_db[column["board_id"]]
            if board["project_id"] in projects_db:
                project = projects_db[board["project_id"]]
                check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    # Update fields
    if "title" in card_data:
        card["title"] = card_data["title"]
    if "description" in card_data:
        card["description"] = card_data["description"]
    if "priority" in card_data:
        card["priority"] = card_data["priority"]

    cards_db[card_id] = card

    return {
        "success": True,
        "data": card,
        "message": "Card updated successfully"
    }


# Card Comments
comments_db = {}

@app.get("/api/v1/cards/{card_id}/comments")
async def get_card_comments(
    card_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get card comments"""
    if card_id not in cards_db:
        raise HTTPException(status_code=404, detail="Card not found")

    user_id = current_user["id"]
    card = cards_db[card_id]

    # Check access
    if card["column_id"] in columns_db:
        column = columns_db[card["column_id"]]
        if column["board_id"] in boards_db:
            board = boards_db[column["board_id"]]
            if board["project_id"] in projects_db:
                project = projects_db[board["project_id"]]
                check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])

    card_comments = [comment for comment in comments_db.values() if comment["card_id"] == card_id]

    return {
        "success": True,
        "data": card_comments
    }


@app.post("/api/v1/cards/{card_id}/comments")
async def add_card_comment(
    card_id: str,
    comment_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Add comment to card"""
    if card_id not in cards_db:
        raise HTTPException(status_code=404, detail="Card not found")

    user_id = current_user["id"]
    card = cards_db[card_id]

    # Check access
    if card["column_id"] in columns_db:
        column = columns_db[card["column_id"]]
        if column["board_id"] in boards_db:
            board = boards_db[column["board_id"]]
            if board["project_id"] in projects_db:
                project = projects_db[board["project_id"]]
                check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

    comment_id = str(uuid.uuid4())
    comment = {
        "id": comment_id,
        "card_id": card_id,
        "content": comment_data.get("content", ""),
        "created_by": user_id,
        "created_at": time.time()
    }

    comments_db[comment_id] = comment

    return {
        "success": True,
        "data": comment,
        "message": "Comment added successfully"
    }


# Team Management Endpoints
teams_db = {}
team_members_db = {}

@app.get("/api/v1/teams")
async def get_teams(
    organization_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get teams"""
    user_id = current_user["id"]
    accessible_teams = []

    for team_id, team in teams_db.items():
        if organization_id and team["organization_id"] != organization_id:
            continue

        try:
            check_organization_access(user_id, team["organization_id"], ["viewer", "member", "admin", "owner"])
            accessible_teams.append(team)
        except HTTPException:
            continue

    return {
        "success": True,
        "data": accessible_teams
    }


@app.post("/api/v1/teams")
async def create_team(
    team_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Create team"""
    user_id = current_user["id"]
    organization_id = team_data.get("organization_id")

    if not organization_id:
        raise HTTPException(status_code=400, detail="organization_id required")

    check_organization_access(user_id, organization_id, ["admin", "owner"])

    team_id = str(uuid.uuid4())
    team = {
        "id": team_id,
        "name": team_data.get("name", "New Team"),
        "description": team_data.get("description", ""),
        "organization_id": organization_id,
        "created_by": user_id,
        "created_at": time.time()
    }

    teams_db[team_id] = team

    return {
        "success": True,
        "data": team,
        "message": "Team created successfully"
    }


@app.put("/api/v1/teams/{team_id}")
async def update_team(
    team_id: str,
    team_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update team"""
    if team_id not in teams_db:
        raise HTTPException(status_code=404, detail="Team not found")

    team = teams_db[team_id]
    user_id = current_user["id"]

    check_organization_access(user_id, team["organization_id"], ["admin", "owner"])

    if "name" in team_data:
        team["name"] = team_data["name"]
    if "description" in team_data:
        team["description"] = team_data["description"]

    teams_db[team_id] = team

    return {
        "success": True,
        "data": team,
        "message": "Team updated successfully"
    }


@app.get("/api/v1/teams/{team_id}/members")
async def get_team_members(
    team_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get team members"""
    if team_id not in teams_db:
        raise HTTPException(status_code=404, detail="Team not found")

    team = teams_db[team_id]
    user_id = current_user["id"]

    check_organization_access(user_id, team["organization_id"], ["viewer", "member", "admin", "owner"])

    members = [member for member in team_members_db.values() if member["team_id"] == team_id]

    return {
        "success": True,
        "data": members
    }


@app.post("/api/v1/teams/{team_id}/members")
async def add_team_member(
    team_id: str,
    member_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Add team member"""
    if team_id not in teams_db:
        raise HTTPException(status_code=404, detail="Team not found")

    team = teams_db[team_id]
    user_id = current_user["id"]

    check_organization_access(user_id, team["organization_id"], ["admin", "owner"])

    member_id = str(uuid.uuid4())
    member = {
        "id": member_id,
        "team_id": team_id,
        "user_email": member_data.get("user_email"),
        "role": member_data.get("role", "member"),
        "added_by": user_id,
        "added_at": time.time()
    }

    team_members_db[member_id] = member

    return {
        "success": True,
        "data": member,
        "message": "Team member added successfully"
    }


# Analytics Endpoints
@app.get("/api/v1/analytics/dashboard")
async def get_analytics_dashboard(current_user: dict = Depends(get_user_from_token)):
    """Get analytics dashboard data"""
    user_id = current_user["id"]

    # Check if user has admin/owner role in any organization
    user_orgs = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["role"] in ["admin", "owner"]:
            user_orgs.append(member["organization_id"])

    if not user_orgs:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    analytics_data = {
        "total_projects": len([p for p in projects_db.values() if p["organization_id"] in user_orgs]),
        "total_boards": len([b for b in boards_db.values() if projects_db.get(b["project_id"], {}).get("organization_id") in user_orgs]),
        "total_cards": len([c for c in cards_db.values()]),
        "active_users": len(set(member["user_id"] for member in organization_members_db.values() if member["organization_id"] in user_orgs))
    }

    return {
        "success": True,
        "data": analytics_data
    }


@app.get("/api/v1/analytics/projects")
async def get_project_analytics(current_user: dict = Depends(get_user_from_token)):
    """Get project analytics"""
    user_id = current_user["id"]

    # Check permissions
    user_orgs = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["role"] in ["admin", "owner"]:
            user_orgs.append(member["organization_id"])

    if not user_orgs:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    project_stats = []
    for project_id, project in projects_db.items():
        if project["organization_id"] in user_orgs:
            project_boards = [b for b in boards_db.values() if b["project_id"] == project_id]
            project_stats.append({
                "project_id": project_id,
                "name": project["name"],
                "boards_count": len(project_boards),
                "status": project["status"]
            })

    return {
        "success": True,
        "data": project_stats
    }


@app.get("/api/v1/analytics/users")
async def get_user_analytics(current_user: dict = Depends(get_user_from_token)):
    """Get user analytics"""
    user_id = current_user["id"]

    # Check permissions
    user_orgs = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["role"] in ["admin", "owner"]:
            user_orgs.append(member["organization_id"])

    if not user_orgs:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    user_stats = []
    for member_id, member in organization_members_db.items():
        if member["organization_id"] in user_orgs:
            user_email = None
            for email, user in users_db.items():
                if user["id"] == member["user_id"]:
                    user_email = email
                    break

            if user_email:
                user_stats.append({
                    "user_id": member["user_id"],
                    "email": user_email,
                    "role": member["role"],
                    "organization_id": member["organization_id"]
                })

    return {
        "success": True,
        "data": user_stats
    }


# Security Endpoints
@app.get("/api/v1/security/audit-logs")
async def get_audit_logs(current_user: dict = Depends(get_user_from_token)):
    """Get audit logs"""
    user_id = current_user["id"]

    # Check permissions
    user_orgs = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["role"] in ["admin", "owner"]:
            user_orgs.append(member["organization_id"])

    if not user_orgs:
        raise HTTPException(status_code=403, detail="Insufficient permissions")

    # Mock audit logs
    audit_logs = [
        {
            "id": str(uuid.uuid4()),
            "action": "user_login",
            "user_id": user_id,
            "timestamp": time.time(),
            "details": "User logged in successfully"
        },
        {
            "id": str(uuid.uuid4()),
            "action": "project_created",
            "user_id": user_id,
            "timestamp": time.time() - 3600,
            "details": "New project created"
        }
    ]

    return {
        "success": True,
        "data": audit_logs
    }


@app.get("/api/v1/security/permissions")
async def get_permissions(current_user: dict = Depends(get_user_from_token)):
    """Get user permissions"""
    user_id = current_user["id"]

    permissions = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                permissions.append({
                    "organization_id": member["organization_id"],
                    "organization_name": org["name"],
                    "role": member["role"],
                    "permissions": get_role_permissions(member["role"])
                })

    return {
        "success": True,
        "data": permissions
    }


def get_role_permissions(role: str) -> list:
    """Get permissions for a role"""
    role_permissions = {
        "owner": ["all"],
        "admin": ["manage_users", "manage_projects", "view_analytics", "manage_teams"],
        "member": ["create_projects", "manage_own_projects", "view_projects"],
        "viewer": ["view_projects", "view_boards"]
    }
    return role_permissions.get(role, [])


# AI & Automation Endpoints
@app.get("/api/v1/ai/models")
async def get_ai_models(current_user: dict = Depends(get_user_from_token)):
    """Get available AI models"""
    models = [
        {
            "id": "text-classifier",
            "name": "Text Classification Model",
            "type": "classification",
            "status": "active"
        },
        {
            "id": "priority-predictor",
            "name": "Priority Prediction Model",
            "type": "prediction",
            "status": "active"
        }
    ]

    return {
        "success": True,
        "data": models
    }


@app.get("/api/v1/ai/workflows")
async def get_ai_workflows(current_user: dict = Depends(get_user_from_token)):
    """Get AI workflows"""
    workflows = [
        {
            "id": str(uuid.uuid4()),
            "name": "Auto-assign Priority",
            "description": "Automatically assign priority to new cards",
            "status": "active"
        },
        {
            "id": str(uuid.uuid4()),
            "name": "Smart Notifications",
            "description": "Send intelligent notifications based on activity",
            "status": "active"
        }
    ]

    return {
        "success": True,
        "data": workflows
    }


# Integration Endpoints
@app.get("/api/v1/integrations")
async def get_integrations(current_user: dict = Depends(get_user_from_token)):
    """Get available integrations"""
    integrations = [
        {
            "id": "slack",
            "name": "Slack",
            "type": "communication",
            "status": "available"
        },
        {
            "id": "github",
            "name": "GitHub",
            "type": "development",
            "status": "available"
        },
        {
            "id": "google-drive",
            "name": "Google Drive",
            "type": "storage",
            "status": "available"
        }
    ]

    return {
        "success": True,
        "data": integrations
    }


@app.post("/api/v1/integrations/{integration_id}/connect")
async def connect_integration(
    integration_id: str,
    connection_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Connect an integration"""
    user_id = current_user["id"]

    # Mock integration connection
    connection = {
        "id": str(uuid.uuid4()),
        "integration_id": integration_id,
        "user_id": user_id,
        "status": "connected",
        "connected_at": time.time()
    }

    return {
        "success": True,
        "data": connection,
        "message": f"Successfully connected to {integration_id}"
    }


# Bulk Operations Endpoints
@app.post("/api/v1/bulk/projects")
async def bulk_update_projects(
    bulk_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Bulk update projects"""
    user_id = current_user["id"]
    operation = bulk_data.get("operation")
    project_ids = bulk_data.get("project_ids", [])
    data = bulk_data.get("data", {})

    updated_projects = []
    for project_id in project_ids:
        if project_id in projects_db:
            project = projects_db[project_id]
            try:
                check_organization_access(user_id, project["organization_id"], ["admin", "owner"])

                if operation == "update_status" and "status" in data:
                    project["status"] = data["status"]
                    projects_db[project_id] = project
                    updated_projects.append(project)
            except HTTPException:
                continue

    return {
        "success": True,
        "data": updated_projects,
        "message": f"Bulk operation completed on {len(updated_projects)} projects"
    }


@app.post("/api/v1/bulk/cards")
async def bulk_update_cards(
    bulk_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Bulk update cards"""
    user_id = current_user["id"]
    operation = bulk_data.get("operation")
    card_ids = bulk_data.get("card_ids", [])
    data = bulk_data.get("data", {})

    updated_cards = []
    for card_id in card_ids:
        if card_id in cards_db:
            card = cards_db[card_id]
            # Check access through card -> column -> board -> project -> organization
            try:
                if card["column_id"] in columns_db:
                    column = columns_db[card["column_id"]]
                    if column["board_id"] in boards_db:
                        board = boards_db[column["board_id"]]
                        if board["project_id"] in projects_db:
                            project = projects_db[board["project_id"]]
                            check_organization_access(user_id, project["organization_id"], ["member", "admin", "owner"])

                            if operation == "update_priority" and "priority" in data:
                                card["priority"] = data["priority"]
                                cards_db[card_id] = card
                                updated_cards.append(card)
            except HTTPException:
                continue

    return {
        "success": True,
        "data": updated_cards,
        "message": f"Bulk operation completed on {len(updated_cards)} cards"
    }


# File Upload Endpoints
@app.post("/api/v1/upload")
async def upload_file(
    file: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Upload file endpoint"""
    if not file:
        raise HTTPException(status_code=422, detail="No file provided")

    # Mock file upload
    file_id = str(uuid.uuid4())
    file_info = {
        "id": file_id,
        "filename": "uploaded_file.txt",
        "size": 1024,
        "type": "text/plain",
        "uploaded_by": current_user["id"],
        "uploaded_at": time.time()
    }

    return {
        "success": True,
        "data": file_info,
        "message": "File uploaded successfully"
    }


# Notification Endpoints
notifications_db = {}

@app.get("/api/v1/users/notifications")
async def get_notifications(current_user: dict = Depends(get_user_from_token)):
    """Get user notifications"""
    user_id = current_user["id"]
    user_notifications = [n for n in notifications_db.values() if n["user_id"] == user_id]

    return {
        "success": True,
        "data": user_notifications
    }


@app.get("/api/v1/users/notifications/preferences")
async def get_notification_preferences(current_user: dict = Depends(get_user_from_token)):
    """Get notification preferences"""
    preferences = {
        "email_notifications": True,
        "push_notifications": True,
        "task_assignments": True,
        "project_updates": True,
        "comment_mentions": True
    }

    return {
        "success": True,
        "data": preferences
    }


@app.put("/api/v1/users/notifications/preferences")
async def update_notification_preferences(
    preferences: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update notification preferences"""
    # Mock update
    return {
        "success": True,
        "data": preferences,
        "message": "Notification preferences updated successfully"
    }


@app.get("/api/v1/users/activity")
async def get_user_activity(current_user: dict = Depends(get_user_from_token)):
    """Get user activity"""
    user_id = current_user["id"]

    # Mock activity data
    activities = [
        {
            "id": str(uuid.uuid4()),
            "type": "project_created",
            "description": "Created a new project",
            "timestamp": time.time() - 3600
        },
        {
            "id": str(uuid.uuid4()),
            "type": "card_updated",
            "description": "Updated card priority",
            "timestamp": time.time() - 7200
        }
    ]

    return {
        "success": True,
        "data": activities
    }


# Organization Settings Endpoints
@app.get("/api/v1/organizations/{org_id}/settings")
async def get_organization_settings(
    org_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get organization settings"""
    user_id = current_user["id"]
    check_organization_access(user_id, org_id, ["admin", "owner"])

    settings = {
        "allowed_domains": ["example.com"],
        "require_2fa": False,
        "project_creation_restricted": False,
        "default_role": "member"
    }

    return {
        "success": True,
        "data": settings
    }


@app.put("/api/v1/organizations/{org_id}/settings")
async def update_organization_settings(
    org_id: str,
    settings_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Update organization settings"""
    user_id = current_user["id"]
    check_organization_access(user_id, org_id, ["owner"])

    # Mock settings update
    return {
        "success": True,
        "data": settings_data,
        "message": "Organization settings updated successfully"
    }


# Project Member Management
@app.get("/api/v1/projects/{project_id}/members")
async def get_project_members(
    project_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get project members"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])

    # Mock project members
    members = [
        {
            "user_id": user_id,
            "email": current_user["email"],
            "role": "owner",
            "joined_at": time.time()
        }
    ]

    return {
        "success": True,
        "data": members
    }


@app.post("/api/v1/projects/{project_id}/members")
async def add_project_member(
    project_id: str,
    member_data: dict,
    current_user: dict = Depends(get_user_from_token)
):
    """Add project member"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    check_organization_access(user_id, project["organization_id"], ["admin", "owner"])

    member = {
        "user_email": member_data.get("user_email"),
        "role": member_data.get("role", "member"),
        "added_by": user_id,
        "added_at": time.time()
    }

    return {
        "success": True,
        "data": member,
        "message": "Project member added successfully"
    }


@app.get("/api/v1/projects/{project_id}/activity")
async def get_project_activity(
    project_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get project activity"""
    if project_id not in projects_db:
        raise HTTPException(status_code=404, detail="Project not found")

    project = projects_db[project_id]
    user_id = current_user["id"]

    check_organization_access(user_id, project["organization_id"], ["viewer", "member", "admin", "owner"])

    activities = [
        {
            "id": str(uuid.uuid4()),
            "type": "project_created",
            "description": f"Project '{project['name']}' was created",
            "timestamp": project["created_at"]
        }
    ]

    return {
        "success": True,
        "data": activities
    }


if __name__ == "__main__":
    import uvicorn
    print(f"🚀 Starting {settings.app_name} (Enhanced Mode)")
    print(f"📍 Server will be available at: http://localhost:3001")
    print(f"📚 API Documentation: http://localhost:3001/docs")
    print(f"🔧 Environment: {settings.environment}")
    print(f"📧 Email configured: {bool(settings.smtp_user and settings.smtp_pass)}")

    uvicorn.run(
        "enhanced_server:app",
        host="0.0.0.0",
        port=3001,
        reload=settings.debug,
        log_level="info"
    )
