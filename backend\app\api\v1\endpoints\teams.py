"""
Team management endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.deps import get_current_active_user, require_admin, require_member
from app.core.exceptions import ResourceNotFoundError, InsufficientPermissionsError, ValidationError
from app.models.user import User
from app.models.organization import OrganizationMember
from app.schemas.organization import OrganizationMemberResponse

router = APIRouter()


@router.get("/{org_id}/members", response_model=List[OrganizationMemberResponse])
async def get_team_members(
    org_id: str,
    search: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    org_member: OrganizationMember = Depends(require_member),
    db: AsyncSession = Depends(get_db)
):
    """Get team members with search and filters"""
    offset = (page - 1) * limit
    
    query = select(OrganizationMember).options(
        selectinload(OrganizationMember.user)
    ).where(OrganizationMember.organization_id == org_id)
    
    # Apply filters
    if role:
        query = query.where(OrganizationMember.role == role)
    
    if search:
        # Search in user's name and email
        query = query.join(User).where(
            (User.first_name.ilike(f"%{search}%")) |
            (User.last_name.ilike(f"%{search}%")) |
            (User.email.ilike(f"%{search}%"))
        )
    
    query = query.offset(offset).limit(limit).order_by(OrganizationMember.joined_at)
    
    result = await db.execute(query)
    members = result.scalars().all()
    
    # Format response
    response = []
    for member in members:
        member_data = OrganizationMemberResponse.from_orm(member)
        member_data.user = {
            "id": str(member.user.id),
            "email": member.user.email,
            "first_name": member.user.first_name,
            "last_name": member.user.last_name,
            "avatar_url": member.user.avatar_url
        }
        response.append(member_data)
    
    return response


@router.get("/{org_id}/members/{user_id}/activity")
async def get_member_activity(
    org_id: str,
    user_id: str,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    org_member: OrganizationMember = Depends(require_member),
    db: AsyncSession = Depends(get_db)
):
    """Get activity for a team member"""
    # Check if target user is member of organization
    target_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == org_id,
            OrganizationMember.user_id == user_id
        )
    )
    if not target_member_result.scalar_one_or_none():
        raise ResourceNotFoundError("User is not a member of this organization")
    
    # TODO: Get activity logs from database
    # For now, return empty list
    return {
        "success": True,
        "data": {
            "items": [],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": 0,
                "totalPages": 0,
                "hasNext": False,
                "hasPrev": False
            }
        }
    }


@router.post("/{org_id}/members/bulk-action")
async def bulk_member_action(
    org_id: str,
    action: str,
    member_ids: List[str],
    current_user: User = Depends(get_current_active_user),
    org_member: OrganizationMember = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Perform bulk actions on team members"""
    if action not in ['remove', 'change_role']:
        raise ValidationError("Invalid action. Must be 'remove' or 'change_role'")
    
    # TODO: Implement bulk actions
    # For now, just return success
    return {
        "success": True,
        "message": f"Bulk action '{action}' completed for {len(member_ids)} members"
    }
