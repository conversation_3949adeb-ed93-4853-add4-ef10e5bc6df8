[{"C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx": "1", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx": "2", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx": "3", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx": "4", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx": "5", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx": "6", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx": "7", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx": "8", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx": "9", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx": "10", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx": "11", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx": "12", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx": "13", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx": "14", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx": "15", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx": "16", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx": "17", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx": "18", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx": "19", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx": "20", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx": "21", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx": "22", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx": "23", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx": "24", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx": "25", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx": "26", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx": "27", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js": "28", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx": "29", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx": "30", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx": "31", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx": "32", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx": "33", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx": "34", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx": "35", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx": "36", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx": "37", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx": "38", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx": "39", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx": "40", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx": "41", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx": "42", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx": "43", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx": "44", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx": "45", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx": "46", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx": "47", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx": "48", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx": "49", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx": "50", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx": "51", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx": "52", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx": "53", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx": "54", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx": "55", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx": "56", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx": "57", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx": "58", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx": "59", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx": "60", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx": "61", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx": "62", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx": "63", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx": "64", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx": "65", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx": "66", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx": "67", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx": "68", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx": "69", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx": "70", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx": "71", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx": "72", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js": "73", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx": "74", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx": "75", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx": "76", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx": "77", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx": "78", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js": "79", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js": "80", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx": "81", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx": "82", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx": "83", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx": "84", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx": "85", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx": "86", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx": "87", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js": "88", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js": "89", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js": "90"}, {"size": 1396, "mtime": 1754034229833, "results": "91", "hashOfConfig": "92"}, {"size": 977, "mtime": 1754033081943, "results": "93", "hashOfConfig": "92"}, {"size": 2002, "mtime": 1754048009332, "results": "94", "hashOfConfig": "92"}, {"size": 2141, "mtime": 1754033238424, "results": "95", "hashOfConfig": "92"}, {"size": 263, "mtime": 1753662156000, "results": "96", "hashOfConfig": "92"}, {"size": 2597, "mtime": 1753662156000, "results": "97", "hashOfConfig": "92"}, {"size": 1443, "mtime": 1753896368000, "results": "98", "hashOfConfig": "92"}, {"size": 1851, "mtime": 1753662960000, "results": "99", "hashOfConfig": "92"}, {"size": 2221, "mtime": 1754033188241, "results": "100", "hashOfConfig": "92"}, {"size": 20809, "mtime": 1754046706906, "results": "101", "hashOfConfig": "92"}, {"size": 9306, "mtime": 1754042965058, "results": "102", "hashOfConfig": "92"}, {"size": 18340, "mtime": 1754043596114, "results": "103", "hashOfConfig": "92"}, {"size": 8178, "mtime": 1754043014618, "results": "104", "hashOfConfig": "92"}, {"size": 12073, "mtime": 1754036144487, "results": "105", "hashOfConfig": "92"}, {"size": 8306, "mtime": 1754042999489, "results": "106", "hashOfConfig": "92"}, {"size": 8401, "mtime": 1754043030852, "results": "107", "hashOfConfig": "92"}, {"size": 29430, "mtime": 1754041061393, "results": "108", "hashOfConfig": "92"}, {"size": 619, "mtime": 1753662156000, "results": "109", "hashOfConfig": "92"}, {"size": 3229, "mtime": 1753931434000, "results": "110", "hashOfConfig": "92"}, {"size": 15516, "mtime": 1754037223798, "results": "111", "hashOfConfig": "92"}, {"size": 3119, "mtime": 1753662156000, "results": "112", "hashOfConfig": "92"}, {"size": 9775, "mtime": 1753662156000, "results": "113", "hashOfConfig": "92"}, {"size": 1661, "mtime": 1753662960000, "results": "114", "hashOfConfig": "92"}, {"size": 8345, "mtime": 1754040316710, "results": "115", "hashOfConfig": "92"}, {"size": 2069, "mtime": 1753662960000, "results": "116", "hashOfConfig": "92"}, {"size": 4355, "mtime": 1753662960000, "results": "117", "hashOfConfig": "92"}, {"size": 1270, "mtime": 1753662960000, "results": "118", "hashOfConfig": "92"}, {"size": 5928, "mtime": 1754033679588, "results": "119", "hashOfConfig": "92"}, {"size": 13829, "mtime": 1754035602790, "results": "120", "hashOfConfig": "92"}, {"size": 3061, "mtime": 1753662960000, "results": "121", "hashOfConfig": "92"}, {"size": 13946, "mtime": 1754050960159, "results": "122", "hashOfConfig": "92"}, {"size": 8743, "mtime": 1754041573665, "results": "123", "hashOfConfig": "92"}, {"size": 5902, "mtime": 1753662960000, "results": "124", "hashOfConfig": "92"}, {"size": 4639, "mtime": 1754041421031, "results": "125", "hashOfConfig": "92"}, {"size": 4319, "mtime": 1753662960000, "results": "126", "hashOfConfig": "92"}, {"size": 3136, "mtime": 1754037270759, "results": "127", "hashOfConfig": "92"}, {"size": 11197, "mtime": 1754052040562, "results": "128", "hashOfConfig": "92"}, {"size": 9431, "mtime": 1754050673764, "results": "129", "hashOfConfig": "92"}, {"size": 2962, "mtime": 1753662960000, "results": "130", "hashOfConfig": "92"}, {"size": 4192, "mtime": 1753662960000, "results": "131", "hashOfConfig": "92"}, {"size": 2269, "mtime": 1753662960000, "results": "132", "hashOfConfig": "92"}, {"size": 8238, "mtime": 1753662960000, "results": "133", "hashOfConfig": "92"}, {"size": 5011, "mtime": 1753662960000, "results": "134", "hashOfConfig": "92"}, {"size": 5667, "mtime": 1753662960000, "results": "135", "hashOfConfig": "92"}, {"size": 6178, "mtime": 1753662960000, "results": "136", "hashOfConfig": "92"}, {"size": 3646, "mtime": 1754043560517, "results": "137", "hashOfConfig": "92"}, {"size": 8113, "mtime": 1754043500588, "results": "138", "hashOfConfig": "92"}, {"size": 6935, "mtime": 1753662960000, "results": "139", "hashOfConfig": "92"}, {"size": 5068, "mtime": 1753662960000, "results": "140", "hashOfConfig": "92"}, {"size": 3503, "mtime": 1754043686958, "results": "141", "hashOfConfig": "92"}, {"size": 10393, "mtime": 1753660406000, "results": "142", "hashOfConfig": "92"}, {"size": 12779, "mtime": 1753660406000, "results": "143", "hashOfConfig": "92"}, {"size": 18772, "mtime": 1753660406000, "results": "144", "hashOfConfig": "92"}, {"size": 16373, "mtime": 1753660406000, "results": "145", "hashOfConfig": "92"}, {"size": 19344, "mtime": 1753660406000, "results": "146", "hashOfConfig": "92"}, {"size": 46641, "mtime": 1754055139623, "results": "147", "hashOfConfig": "92"}, {"size": 5370, "mtime": 1753663554000, "results": "148", "hashOfConfig": "92"}, {"size": 5637, "mtime": 1753663554000, "results": "149", "hashOfConfig": "92"}, {"size": 1689, "mtime": 1753663554000, "results": "150", "hashOfConfig": "92"}, {"size": 5556, "mtime": 1753663554000, "results": "151", "hashOfConfig": "92"}, {"size": 17413, "mtime": 1753667086000, "results": "152", "hashOfConfig": "92"}, {"size": 13209, "mtime": 1753667086000, "results": "153", "hashOfConfig": "92"}, {"size": 19464, "mtime": 1753667086000, "results": "154", "hashOfConfig": "92"}, {"size": 7859, "mtime": 1753660406000, "results": "155", "hashOfConfig": "92"}, {"size": 13656, "mtime": 1753660406000, "results": "156", "hashOfConfig": "92"}, {"size": 13718, "mtime": 1753660406000, "results": "157", "hashOfConfig": "92"}, {"size": 2946, "mtime": 1753916458000, "results": "158", "hashOfConfig": "92"}, {"size": 5117, "mtime": 1753916458000, "results": "159", "hashOfConfig": "92"}, {"size": 3744, "mtime": 1753916090000, "results": "160", "hashOfConfig": "92"}, {"size": 5879, "mtime": 1753916458000, "results": "161", "hashOfConfig": "92"}, {"size": 6371, "mtime": 1753979035139, "results": "162", "hashOfConfig": "92"}, {"size": 5794, "mtime": 1753916090000, "results": "163", "hashOfConfig": "92"}, {"size": 139, "mtime": 1753662156000, "results": "164", "hashOfConfig": "92"}, {"size": 7859, "mtime": 1753916090000, "results": "165", "hashOfConfig": "92"}, {"size": 5802, "mtime": 1753916090000, "results": "166", "hashOfConfig": "92"}, {"size": 4753, "mtime": 1753662156000, "results": "167", "hashOfConfig": "92"}, {"size": 329, "mtime": 1753662156000, "results": "168", "hashOfConfig": "92"}, {"size": 6210, "mtime": 1754041438145, "results": "169", "hashOfConfig": "92"}, {"size": 9964, "mtime": 1754042941881, "results": "170", "hashOfConfig": "92"}, {"size": 8837, "mtime": 1754035271036, "results": "171", "hashOfConfig": "92"}, {"size": 4743, "mtime": 1753979063817, "results": "172", "hashOfConfig": "92"}, {"size": 623, "mtime": 1753979082425, "results": "173", "hashOfConfig": "92"}, {"size": 5337, "mtime": 1753979469770, "results": "174", "hashOfConfig": "92"}, {"size": 8078, "mtime": 1754026444720, "results": "175", "hashOfConfig": "92"}, {"size": 2645, "mtime": 1754026405389, "results": "176", "hashOfConfig": "92"}, {"size": 8796, "mtime": 1754030789819, "results": "177", "hashOfConfig": "92"}, {"size": 18908, "mtime": 1754048360655, "results": "178", "hashOfConfig": "92"}, {"size": 5668, "mtime": 1754050276943, "results": "179", "hashOfConfig": "92"}, {"size": 9462, "mtime": 1754050330607, "results": "180", "hashOfConfig": "92"}, {"size": 8055, "mtime": 1754050911094, "results": "181", "hashOfConfig": "92"}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7s4ywu", {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx", ["452", "453", "454"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx", ["455"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx", ["456"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx", ["457", "458", "459"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx", ["460"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx", ["461", "462"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx", ["463"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\KeyboardShortcutsContext.jsx", ["464", "465"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\ThemeContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\accessibility\\AccessibilityProvider.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-overview\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\rolePermissions.js", ["466", "467"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\aiChecklistService.js", ["468"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\errorHandling.js", ["469"], [], {"ruleId": "470", "severity": 1, "message": "471", "line": 20, "column": 11, "nodeType": "472", "messageId": "473", "endLine": 20, "endColumn": 15}, {"ruleId": "470", "severity": 1, "message": "474", "line": 299, "column": 9, "nodeType": "472", "messageId": "473", "endLine": 299, "endColumn": 21}, {"ruleId": "470", "severity": 1, "message": "475", "line": 304, "column": 9, "nodeType": "472", "messageId": "473", "endLine": 304, "endColumn": 23}, {"ruleId": "470", "severity": 1, "message": "476", "line": 14, "column": 27, "nodeType": "472", "messageId": "473", "endLine": 14, "endColumn": 45}, {"ruleId": "470", "severity": 1, "message": "477", "line": 8, "column": 49, "nodeType": "472", "messageId": "473", "endLine": 8, "endColumn": 60}, {"ruleId": "470", "severity": 1, "message": "478", "line": 4, "column": 31, "nodeType": "472", "messageId": "473", "endLine": 4, "endColumn": 48}, {"ruleId": "470", "severity": 1, "message": "479", "line": 12, "column": 10, "nodeType": "472", "messageId": "473", "endLine": 12, "endColumn": 25}, {"ruleId": "470", "severity": 1, "message": "480", "line": 65, "column": 9, "nodeType": "472", "messageId": "473", "endLine": 65, "endColumn": 31}, {"ruleId": "470", "severity": 1, "message": "481", "line": 20, "column": 10, "nodeType": "472", "messageId": "473", "endLine": 20, "endColumn": 18}, {"ruleId": "482", "severity": 1, "message": "483", "line": 158, "column": 5, "nodeType": "484", "messageId": "485", "endLine": 176, "endColumn": 6}, {"ruleId": "470", "severity": 1, "message": "486", "line": 188, "column": 9, "nodeType": "472", "messageId": "473", "endLine": 188, "endColumn": 21}, {"ruleId": "470", "severity": 1, "message": "487", "line": 129, "column": 9, "nodeType": "472", "messageId": "473", "endLine": 129, "endColumn": 24}, {"ruleId": "488", "severity": 1, "message": "489", "line": 135, "column": 6, "nodeType": "490", "endLine": 135, "endColumn": 62, "suggestions": "491"}, {"ruleId": "488", "severity": 1, "message": "492", "line": 151, "column": 6, "nodeType": "490", "endLine": 151, "endColumn": 8, "suggestions": "493"}, {"ruleId": "470", "severity": 1, "message": "494", "line": 124, "column": 9, "nodeType": "472", "messageId": "473", "endLine": 124, "endColumn": 20}, {"ruleId": "495", "severity": 1, "message": "496", "line": 185, "column": 1, "nodeType": "497", "endLine": 193, "endColumn": 3}, {"ruleId": "495", "severity": 1, "message": "496", "line": 315, "column": 1, "nodeType": "497", "endLine": 319, "endColumn": 3}, {"ruleId": "495", "severity": 1, "message": "496", "line": 250, "column": 1, "nodeType": "497", "endLine": 260, "endColumn": 3}, "no-unused-vars", "'user' is assigned a value but never used.", "Identifier", "unusedVar", "'canEditCards' is assigned a value but never used.", "'canDeleteCards' is assigned a value but never used.", "'setSidebarExpanded' is assigned a value but never used.", "'handleError' is defined but never used.", "'getSuggestedItems' is defined but never used.", "'showSuggestions' is assigned a value but never used.", "'handleAddSuggestedItem' is assigned a value but never used.", "'logoFile' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'getRoleColor' is assigned a value but never used.", "'priorityOptions' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", "ArrayExpression", ["498"], "React Hook useEffect has a missing dependency: 'defaultShortcuts'. Either include it or remove the dependency array.", ["499"], "'permissions' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "500", "fix": "501"}, {"desc": "502", "fix": "503"}, "Update the dependencies array to be: [defaultShortcuts, sequenceTimeout, keySequence]", {"range": "504", "text": "505"}, "Update the dependencies array to be: [defaultShortcuts]", {"range": "506", "text": "507"}, [4586, 4642], "[defaultShortcuts, sequenceTimeout, keySequence]", [5058, 5060], "[defaultShortcuts]"]