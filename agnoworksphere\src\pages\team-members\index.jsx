import React, { useState, useMemo, useEffect } from 'react';
import RoleBasedHeader from '../../components/ui/RoleBasedHeader';
import Breadcrumb from '../../components/ui/Breadcrumb';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Input from '../../components/ui/Input';
import Select from '../../components/ui/Select';
import MemberTable from './components/MemberTable';
import MemberCard from './components/MemberCard';
import InviteMemberModal from './components/InviteMemberModal';
import EditRoleModal from './components/EditRoleModal';
import MemberActivityModal from './components/MemberActivityModal';
import RemoveMemberModal from './components/RemoveMemberModal';
import BulkActionsBar from './components/BulkActionsBar';
import authService from '../../utils/authService';

const TeamMembers = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'asc' });
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'cards'
  const [selectedMembers, setSelectedMembers] = useState([]);
  
  // Modal states
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [isEditRoleModalOpen, setIsEditRoleModalOpen] = useState(false);
  const [isActivityModalOpen, setIsActivityModalOpen] = useState(false);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState(null);

  // Authentication state
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState('member');
  const [currentOrganization, setCurrentOrganization] = useState(null);

  // Get role-specific features
  const getRoleFeatures = (role) => {
    const features = {
      viewer: {
        canInviteMembers: false,
        canManageMembers: false,
        canRemoveMembers: false
      },
      member: {
        canInviteMembers: false,
        canManageMembers: false,
        canRemoveMembers: false
      },
      admin: {
        canInviteMembers: true,
        canManageMembers: true,
        canRemoveMembers: true
      },
      owner: {
        canInviteMembers: true,
        canManageMembers: true,
        canRemoveMembers: true
      }
    };
    return features[role.toLowerCase()] || features.member;
  };

  const roleFeatures = getRoleFeatures(userRole);

  // Mock data for team members
  const [members, setMembers] = useState([
    {
      id: 1,
      name: "Sarah Johnson",
      email: "<EMAIL>",
      role: "Owner",
      status: "active",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face",
      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),
      joinedDate: new Date('2023-01-15')
    },
    {
      id: 2,
      name: "Michael Chen",
      email: "<EMAIL>",
      role: "Admin",
      status: "active",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face",
      lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000),
      joinedDate: new Date('2023-02-20')
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      email: "<EMAIL>",
      role: "Member",
      status: "active",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face",
      lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000),
      joinedDate: new Date('2023-03-10')
    },
    {
      id: 4,
      name: "David Kim",
      email: "<EMAIL>",
      role: "Member",
      status: "inactive",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face",
      lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000),
      joinedDate: new Date('2023-04-05')
    },
    {
      id: 5,
      name: "Lisa Thompson",
      email: "<EMAIL>",
      role: "Viewer",
      status: "active",
      avatar: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face",
      lastActivity: new Date(Date.now() - 8 * 60 * 60 * 1000),
      joinedDate: new Date('2023-05-12')
    },
    {
      id: 6,
      name: "James Wilson",
      email: "<EMAIL>",
      role: "Admin",
      status: "pending",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face",
      lastActivity: new Date(Date.now() - 12 * 60 * 60 * 1000),
      joinedDate: new Date('2023-06-18')
    },
    {
      id: 7,
      name: "Anna Martinez",
      email: "<EMAIL>",
      role: "Member",
      status: "active",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face",
      lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000),
      joinedDate: new Date('2023-07-22')
    },
    {
      id: 8,
      name: "Robert Davis",
      email: "<EMAIL>",
      role: "Viewer",
      status: "active",
      avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face",
      lastActivity: new Date(Date.now() - 3 * 60 * 60 * 1000),
      joinedDate: new Date('2023-08-14')
    }
  ]);

  const roleOptions = [
    { value: 'all', label: 'All Roles' },
    { value: 'owner', label: 'Owner' },
    { value: 'admin', label: 'Admin' },
    { value: 'member', label: 'Member' },
    { value: 'viewer', label: 'Viewer' }
  ];

  const statusOptions = [
    { value: 'all', label: 'All Status' },
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'pending', label: 'Pending' }
  ];

  // Filter and sort members
  const filteredAndSortedMembers = useMemo(() => {
    let filtered = members.filter(member => {
      const matchesSearch = member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           member.email.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesRole = roleFilter === 'all' || member.role.toLowerCase() === roleFilter;
      const matchesStatus = statusFilter === 'all' || member.status.toLowerCase() === statusFilter;
      
      return matchesSearch && matchesRole && matchesStatus;
    });

    // Sort members
    filtered.sort((a, b) => {
      let aValue = a[sortConfig.key];
      let bValue = b[sortConfig.key];

      if (sortConfig.key === 'lastActivity') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      }

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });

    return filtered;
  }, [members, searchQuery, roleFilter, statusFilter, sortConfig]);

  const handleSort = (key) => {
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleSelectMember = (memberId) => {
    setSelectedMembers(prev => 
      prev.includes(memberId) 
        ? prev.filter(id => id !== memberId)
        : [...prev, memberId]
    );
  };

  const handleSelectAll = () => {
    if (selectedMembers.length === filteredAndSortedMembers.length) {
      setSelectedMembers([]);
    } else {
      setSelectedMembers(filteredAndSortedMembers.map(member => member.id));
    }
  };

  const handleInviteMembers = async (inviteData) => {
    console.log('Inviting members:', inviteData);
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Add mock invited members to the list
    const newMembers = inviteData.emails.map((email, index) => ({
      id: Date.now() + index,
      name: email.split('@')[0].replace('.', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      email,
      role: inviteData.role,
      status: 'pending',
      avatar: `https://images.unsplash.com/photo-150000000${index + 1}?w=150&h=150&fit=crop&crop=face`,
      lastActivity: new Date(),
      joinedDate: new Date()
    }));
    
    setMembers(prev => [...prev, ...newMembers]);
  };

  const handleEditRole = (member) => {
    setSelectedMember(member);
    setIsEditRoleModalOpen(true);
  };

  const handleUpdateRole = async (memberId, newRole) => {
    console.log('Updating role:', memberId, newRole);
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    setMembers(prev => prev.map(member => 
      member.id === memberId ? { ...member, role: newRole } : member
    ));
  };

  const handleViewActivity = (member) => {
    setSelectedMember(member);
    setIsActivityModalOpen(true);
  };

  const handleRemoveMember = (member) => {
    setSelectedMember(member);
    setIsRemoveModalOpen(true);
  };

  const handleConfirmRemove = async (memberId) => {
    console.log('Removing member:', memberId);
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    setMembers(prev => prev.filter(member => member.id !== memberId));
    setSelectedMembers(prev => prev.filter(id => id !== memberId));
  };

  const handleBulkRoleChange = async (newRole) => {
    console.log('Bulk role change:', selectedMembers, newRole);
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setMembers(prev => prev.map(member => 
      selectedMembers.includes(member.id) ? { ...member, role: newRole } : member
    ));
    setSelectedMembers([]);
  };

  const handleBulkRemove = async () => {
    console.log('Bulk remove:', selectedMembers);
    // Mock API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setMembers(prev => prev.filter(member => !selectedMembers.includes(member.id)));
    setSelectedMembers([]);
  };

  const handleClearSelection = () => {
    setSelectedMembers([]);
  };

  // Load user authentication data
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const userResponse = await authService.getCurrentUser();
        const orgResponse = await authService.getCurrentOrganization();

        if (userResponse.data && userResponse.data.user) {
          setCurrentUser(userResponse.data.user);
          setUserRole(userResponse.data.user.role || 'member');
        }

        if (orgResponse.data && orgResponse.data.organization) {
          setCurrentOrganization(orgResponse.data.organization);
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        setUserRole('member');
      }
    };

    loadUserData();
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <RoleBasedHeader
        userRole={userRole.toLowerCase()}
        currentUser={currentUser ? {
          name: `${currentUser.firstName} ${currentUser.lastName}`,
          email: currentUser.email,
          avatar: currentUser.avatar || '/assets/images/avatar.jpg',
          role: userRole
        } : {
          name: 'Loading...',
          email: '',
          avatar: '/assets/images/avatar.jpg',
          role: userRole
        }}
        currentOrganization={currentOrganization}
      />

      <main className="pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Breadcrumb />

          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <Icon name="Users" size={20} className="text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-foreground">Team Members</h1>
                <p className="text-muted-foreground">
                  Manage your organization's team members, roles, and permissions
                </p>
              </div>
            </div>
            {roleFeatures.canInviteMembers && (
              <div className="flex justify-end">
                <Button
                  onClick={() => setIsInviteModalOpen(true)}
                  iconName="UserPlus"
                  iconPosition="left"
                >
                  Invite Members
                </Button>
              </div>
            )}
          </div>

        {/* Filters and Search */}
        <div className="bg-card border border-border rounded-lg p-6 mb-6 shadow-ambient">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
              <div className="w-full sm:w-80">
                <Input
                  type="search"
                  placeholder="Search members by name or email..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full"
                />
              </div>
              <div className="flex space-x-3">
                <Select
                  placeholder="Filter by role"
                  options={roleOptions}
                  value={roleFilter}
                  onChange={setRoleFilter}
                  className="min-w-[140px]"
                />
                <Select
                  placeholder="Filter by status"
                  options={statusOptions}
                  value={statusFilter}
                  onChange={setStatusFilter}
                  className="min-w-[140px]"
                />
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <div className="flex items-center bg-muted rounded-lg p-1">
                <Button
                  variant={viewMode === 'table' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('table')}
                  iconName="Table"
                  className="h-8 w-8"
                />
                <Button
                  variant={viewMode === 'cards' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('cards')}
                  iconName="Grid3X3"
                  className="h-8 w-8"
                />
              </div>
              <div className="text-sm text-text-secondary">
                {filteredAndSortedMembers.length} of {members.length} members
              </div>
            </div>
          </div>
        </div>

        {/* Bulk Actions */}
        <BulkActionsBar
          selectedCount={selectedMembers.length}
          onBulkRoleChange={roleFeatures.canManageMembers ? handleBulkRoleChange : null}
          onBulkRemove={roleFeatures.canRemoveMembers ? handleBulkRemove : null}
          onClearSelection={handleClearSelection}
          canManageMembers={roleFeatures.canManageMembers}
          canRemoveMembers={roleFeatures.canRemoveMembers}
        />

        {/* Members List */}
        {viewMode === 'table' ? (
          <MemberTable
            members={filteredAndSortedMembers}
            sortConfig={sortConfig}
            onSort={handleSort}
            onEditRole={roleFeatures.canManageMembers ? handleEditRole : null}
            onViewActivity={handleViewActivity}
            onRemoveMember={roleFeatures.canRemoveMembers ? handleRemoveMember : null}
            selectedMembers={selectedMembers}
            onSelectMember={handleSelectMember}
            onSelectAll={handleSelectAll}
            canManageMembers={roleFeatures.canManageMembers}
            canRemoveMembers={roleFeatures.canRemoveMembers}
          />
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredAndSortedMembers.map((member) => (
              <MemberCard
                key={member.id}
                member={member}
                onEditRole={roleFeatures.canManageMembers ? handleEditRole : null}
                onViewActivity={handleViewActivity}
                onRemoveMember={roleFeatures.canRemoveMembers ? handleRemoveMember : null}
                canManageMembers={roleFeatures.canManageMembers}
                canRemoveMembers={roleFeatures.canRemoveMembers}
              />
            ))}
          </div>
        )}

        {/* Empty State */}
        {filteredAndSortedMembers.length === 0 && (
          <div className="text-center py-12">
            <Icon name="Users" size={48} className="text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text-primary mb-2">No members found</h3>
            <p className="text-text-secondary mb-6">
              {searchQuery || roleFilter !== 'all' || statusFilter !== 'all' ?'Try adjusting your search or filter criteria' :'Get started by inviting your first team member'}
            </p>
            {(!searchQuery && roleFilter === 'all' && statusFilter === 'all') && roleFeatures.canInviteMembers && (
              <Button
                onClick={() => setIsInviteModalOpen(true)}
                iconName="UserPlus"
                iconPosition="left"
              >
                Invite Members
              </Button>
            )}
          </div>
        )}

        {/* Modals */}
        <InviteMemberModal
          isOpen={isInviteModalOpen}
          onClose={() => setIsInviteModalOpen(false)}
          onInvite={handleInviteMembers}
        />

        <EditRoleModal
          isOpen={isEditRoleModalOpen}
          onClose={() => setIsEditRoleModalOpen(false)}
          member={selectedMember}
          onUpdateRole={handleUpdateRole}
        />

        <MemberActivityModal
          isOpen={isActivityModalOpen}
          onClose={() => setIsActivityModalOpen(false)}
          member={selectedMember}
        />

        <RemoveMemberModal
          isOpen={isRemoveModalOpen}
          onClose={() => setIsRemoveModalOpen(false)}
          member={selectedMember}
          onRemove={handleConfirmRemove}
        />
        </div>
      </main>
    </div>
  );
};

export default TeamMembers;