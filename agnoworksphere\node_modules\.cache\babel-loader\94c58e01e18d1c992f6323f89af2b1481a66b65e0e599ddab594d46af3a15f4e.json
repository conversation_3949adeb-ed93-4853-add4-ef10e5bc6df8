{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\ui\\\\ProfessionalLayout.jsx\";\n/**\n * Professional Layout Components for Owner Interface\n * Enterprise-grade layout system with responsive design and accessibility\n */\nimport React from 'react';\nimport Icon from '../AppIcon';\n\n// Main Container Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Container = ({\n  children,\n  size = 'default',\n  className = '',\n  ...props\n}) => {\n  const sizes = {\n    sm: 'max-w-3xl',\n    default: 'max-w-7xl',\n    lg: 'max-w-full',\n    full: 'w-full'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `mx-auto px-4 sm:px-6 lg:px-8 ${sizes[size]} ${className}`,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n\n// Grid System\n_c = Container;\nexport const Grid = ({\n  children,\n  cols = 1,\n  gap = 6,\n  className = '',\n  responsive = true,\n  ...props\n}) => {\n  const gridCols = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-1 md:grid-cols-2',\n    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',\n    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',\n    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'\n  };\n  const responsiveClass = responsive ? gridCols[cols] : `grid-cols-${cols}`;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `grid ${responsiveClass} gap-${gap} ${className}`,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n};\n\n// Flex Layout Components\n_c2 = Grid;\nexport const Flex = ({\n  children,\n  direction = 'row',\n  align = 'start',\n  justify = 'start',\n  wrap = false,\n  gap = 0,\n  className = '',\n  ...props\n}) => {\n  const directions = {\n    row: 'flex-row',\n    col: 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse'\n  };\n  const alignments = {\n    start: 'items-start',\n    center: 'items-center',\n    end: 'items-end',\n    stretch: 'items-stretch',\n    baseline: 'items-baseline'\n  };\n  const justifications = {\n    start: 'justify-start',\n    center: 'justify-center',\n    end: 'justify-end',\n    between: 'justify-between',\n    around: 'justify-around',\n    evenly: 'justify-evenly'\n  };\n  const wrapClass = wrap ? 'flex-wrap' : '';\n  const gapClass = gap > 0 ? `gap-${gap}` : '';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n        flex ${directions[direction]} ${alignments[align]} \n        ${justifications[justify]} ${wrapClass} ${gapClass} ${className}\n      `,\n    ...props,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 5\n  }, this);\n};\n\n// Stack Component (Vertical spacing)\n_c3 = Flex;\nexport const Stack = ({\n  children,\n  spacing = 4,\n  className = '',\n  ...props\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `space-y-${spacing} ${className}`,\n  ...props,\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 120,\n  columnNumber: 3\n}, this);\n\n// Page Header Component\n_c4 = Stack;\nexport const PageHeader = ({\n  title,\n  subtitle,\n  action,\n  breadcrumbs,\n  variant = 'default',\n  className = ''\n}) => {\n  const variants = {\n    default: 'bg-white border-b border-gray-200',\n    owner: 'bg-gradient-to-r from-purple-50 to-pink-50 border-b border-purple-200',\n    minimal: 'bg-transparent'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${variants[variant]} ${className}`,\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"py-6 lg:py-8\",\n        children: [breadcrumbs && /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"ol\", {\n            className: \"flex items-center space-x-2 text-sm\",\n            children: breadcrumbs.map((crumb, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n              className: \"flex items-center\",\n              children: [index > 0 && /*#__PURE__*/_jsxDEV(Icon, {\n                name: \"ChevronRight\",\n                size: 14,\n                className: \"text-gray-400 mx-2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 23\n              }, this), crumb.href ? /*#__PURE__*/_jsxDEV(\"a\", {\n                href: crumb.href,\n                className: `\n                          hover:text-gray-900 transition-colors\n                          ${variant === 'owner' ? 'text-purple-600' : 'text-gray-500'}\n                        `,\n                children: crumb.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 23\n              }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-900 font-medium\",\n                children: crumb.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 23\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 145,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Flex, {\n          justify: \"between\",\n          align: \"start\",\n          className: \"flex-wrap gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"min-w-0 flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: `\n                text-3xl font-bold tracking-tight\n                ${variant === 'owner' ? 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent' : 'text-gray-900'}\n              `,\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-lg text-gray-600\",\n              children: subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), action && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: action\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n\n// Section Component\n_c5 = PageHeader;\nexport const Section = ({\n  children,\n  title,\n  subtitle,\n  action,\n  spacing = 'default',\n  className = '',\n  ...props\n}) => {\n  const spacings = {\n    sm: 'py-8',\n    default: 'py-12',\n    lg: 'py-16',\n    xl: 'py-20'\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: `${spacings[spacing]} ${className}`,\n    ...props,\n    children: /*#__PURE__*/_jsxDEV(Container, {\n      children: [(title || subtitle || action) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(Flex, {\n          justify: \"between\",\n          align: \"start\",\n          className: \"mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [title && /*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 19\n            }, this), subtitle && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-gray-600\",\n              children: subtitle\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), action && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: action\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 26\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 11\n      }, this), children]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 218,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 217,\n    columnNumber: 5\n  }, this);\n};\n\n// Owner Dashboard Layout\n_c6 = Section;\nexport const OwnerDashboardLayout = ({\n  children,\n  sidebar,\n  header,\n  className = ''\n}) => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: `min-h-screen bg-gray-50 ${className}`,\n  children: [header, /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex\",\n    children: [sidebar && /*#__PURE__*/_jsxDEV(\"aside\", {\n      className: \"hidden lg:block w-64 bg-white border-r border-gray-200 min-h-screen\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6\",\n        children: sidebar\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 251,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-1 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-6 lg:p-8\",\n        children: children\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 258,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 249,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 247,\n  columnNumber: 3\n}, this);\n\n// Card Grid Layout\n_c7 = OwnerDashboardLayout;\nexport const CardGrid = ({\n  children,\n  cols = 3,\n  gap = 6,\n  className = ''\n}) => /*#__PURE__*/_jsxDEV(Grid, {\n  cols: cols,\n  gap: gap,\n  className: className,\n  children: children\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 273,\n  columnNumber: 3\n}, this);\n\n// Two Column Layout\n_c8 = CardGrid;\nexport const TwoColumnLayout = ({\n  left,\n  right,\n  leftWidth = '2/3',\n  gap = 8,\n  className = ''\n}) => {\n  const widths = {\n    '1/3': 'lg:w-1/3',\n    '1/2': 'lg:w-1/2',\n    '2/3': 'lg:w-2/3',\n    '3/4': 'lg:w-3/4'\n  };\n  const rightWidth = leftWidth === '2/3' ? '1/3' : leftWidth === '1/2' ? '1/2' : leftWidth === '1/3' ? '2/3' : '1/4';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex flex-col lg:flex-row gap-${gap} ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${widths[leftWidth]}`,\n      children: left\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 299,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `${widths[rightWidth]}`,\n      children: right\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 298,\n    columnNumber: 5\n  }, this);\n};\n\n// Responsive Sidebar Layout\n_c9 = TwoColumnLayout;\nexport const SidebarLayout = ({\n  children,\n  sidebar,\n  sidebarPosition = 'left',\n  sidebarWidth = 'w-64',\n  className = ''\n}) => {\n  const isLeft = sidebarPosition === 'left';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isLeft ? 'flex-row' : 'flex-row-reverse'} ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"aside\", {\n      className: `hidden lg:block ${sidebarWidth} flex-shrink-0`,\n      children: sidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"flex-1 min-w-0\",\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 324,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 320,\n    columnNumber: 5\n  }, this);\n};\n\n// Centered Content Layout\n_c0 = SidebarLayout;\nexport const CenteredLayout = ({\n  children,\n  maxWidth = 'md',\n  className = ''\n}) => {\n  const maxWidths = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `min-h-screen flex items-center justify-center p-4 ${className}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `w-full ${maxWidths[maxWidth]}`,\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 346,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 345,\n    columnNumber: 5\n  }, this);\n};\n_c1 = CenteredLayout;\nexport default {\n  Container,\n  Grid,\n  Flex,\n  Stack,\n  PageHeader,\n  Section,\n  OwnerDashboardLayout,\n  CardGrid,\n  TwoColumnLayout,\n  SidebarLayout,\n  CenteredLayout\n};\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c0, _c1;\n$RefreshReg$(_c, \"Container\");\n$RefreshReg$(_c2, \"Grid\");\n$RefreshReg$(_c3, \"Flex\");\n$RefreshReg$(_c4, \"Stack\");\n$RefreshReg$(_c5, \"PageHeader\");\n$RefreshReg$(_c6, \"Section\");\n$RefreshReg$(_c7, \"OwnerDashboardLayout\");\n$RefreshReg$(_c8, \"CardGrid\");\n$RefreshReg$(_c9, \"TwoColumnLayout\");\n$RefreshReg$(_c0, \"SidebarLayout\");\n$RefreshReg$(_c1, \"CenteredLayout\");", "map": {"version": 3, "names": ["React", "Icon", "jsxDEV", "_jsxDEV", "Container", "children", "size", "className", "props", "sizes", "sm", "default", "lg", "full", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "Grid", "cols", "gap", "responsive", "gridCols", "responsiveClass", "_c2", "Flex", "direction", "align", "justify", "wrap", "directions", "row", "col", "alignments", "start", "center", "end", "stretch", "baseline", "justifications", "between", "around", "evenly", "wrapClass", "gapClass", "_c3", "<PERSON><PERSON>", "spacing", "_c4", "<PERSON><PERSON><PERSON><PERSON>", "title", "subtitle", "action", "breadcrumbs", "variant", "variants", "owner", "minimal", "map", "crumb", "index", "name", "href", "label", "_c5", "Section", "spacings", "xl", "_c6", "OwnerDashboardLayout", "sidebar", "header", "_c7", "CardGrid", "_c8", "TwoColumnLayout", "left", "right", "leftWidth", "widths", "rightWidth", "_c9", "SidebarLayout", "sidebarPosition", "sidebarWidth", "isLeft", "_c0", "CenteredLayout", "max<PERSON><PERSON><PERSON>", "maxWid<PERSON>", "md", "_c1", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/ui/ProfessionalLayout.jsx"], "sourcesContent": ["/**\n * Professional Layout Components for Owner Interface\n * Enterprise-grade layout system with responsive design and accessibility\n */\nimport React from 'react';\nimport Icon from '../AppIcon';\n\n// Main Container Component\nexport const Container = ({ \n  children, \n  size = 'default',\n  className = '',\n  ...props \n}) => {\n  const sizes = {\n    sm: 'max-w-3xl',\n    default: 'max-w-7xl',\n    lg: 'max-w-full',\n    full: 'w-full'\n  };\n\n  return (\n    <div \n      className={`mx-auto px-4 sm:px-6 lg:px-8 ${sizes[size]} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Grid System\nexport const Grid = ({ \n  children, \n  cols = 1,\n  gap = 6,\n  className = '',\n  responsive = true,\n  ...props \n}) => {\n  const gridCols = {\n    1: 'grid-cols-1',\n    2: 'grid-cols-1 md:grid-cols-2',\n    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',\n    5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',\n    6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'\n  };\n\n  const responsiveClass = responsive ? gridCols[cols] : `grid-cols-${cols}`;\n\n  return (\n    <div \n      className={`grid ${responsiveClass} gap-${gap} ${className}`}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Flex Layout Components\nexport const Flex = ({ \n  children, \n  direction = 'row',\n  align = 'start',\n  justify = 'start',\n  wrap = false,\n  gap = 0,\n  className = '',\n  ...props \n}) => {\n  const directions = {\n    row: 'flex-row',\n    col: 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse'\n  };\n\n  const alignments = {\n    start: 'items-start',\n    center: 'items-center',\n    end: 'items-end',\n    stretch: 'items-stretch',\n    baseline: 'items-baseline'\n  };\n\n  const justifications = {\n    start: 'justify-start',\n    center: 'justify-center',\n    end: 'justify-end',\n    between: 'justify-between',\n    around: 'justify-around',\n    evenly: 'justify-evenly'\n  };\n\n  const wrapClass = wrap ? 'flex-wrap' : '';\n  const gapClass = gap > 0 ? `gap-${gap}` : '';\n\n  return (\n    <div \n      className={`\n        flex ${directions[direction]} ${alignments[align]} \n        ${justifications[justify]} ${wrapClass} ${gapClass} ${className}\n      `}\n      {...props}\n    >\n      {children}\n    </div>\n  );\n};\n\n// Stack Component (Vertical spacing)\nexport const Stack = ({ \n  children, \n  spacing = 4,\n  className = '',\n  ...props \n}) => (\n  <div className={`space-y-${spacing} ${className}`} {...props}>\n    {children}\n  </div>\n);\n\n// Page Header Component\nexport const PageHeader = ({ \n  title, \n  subtitle, \n  action,\n  breadcrumbs,\n  variant = 'default',\n  className = ''\n}) => {\n  const variants = {\n    default: 'bg-white border-b border-gray-200',\n    owner: 'bg-gradient-to-r from-purple-50 to-pink-50 border-b border-purple-200',\n    minimal: 'bg-transparent'\n  };\n\n  return (\n    <div className={`${variants[variant]} ${className}`}>\n      <Container>\n        <div className=\"py-6 lg:py-8\">\n          {breadcrumbs && (\n            <nav className=\"mb-4\">\n              <ol className=\"flex items-center space-x-2 text-sm\">\n                {breadcrumbs.map((crumb, index) => (\n                  <li key={index} className=\"flex items-center\">\n                    {index > 0 && (\n                      <Icon name=\"ChevronRight\" size={14} className=\"text-gray-400 mx-2\" />\n                    )}\n                    {crumb.href ? (\n                      <a \n                        href={crumb.href}\n                        className={`\n                          hover:text-gray-900 transition-colors\n                          ${variant === 'owner' ? 'text-purple-600' : 'text-gray-500'}\n                        `}\n                      >\n                        {crumb.label}\n                      </a>\n                    ) : (\n                      <span className=\"text-gray-900 font-medium\">{crumb.label}</span>\n                    )}\n                  </li>\n                ))}\n              </ol>\n            </nav>\n          )}\n          \n          <Flex justify=\"between\" align=\"start\" className=\"flex-wrap gap-4\">\n            <div className=\"min-w-0 flex-1\">\n              <h1 className={`\n                text-3xl font-bold tracking-tight\n                ${variant === 'owner' \n                  ? 'bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent' \n                  : 'text-gray-900'\n                }\n              `}>\n                {title}\n              </h1>\n              {subtitle && (\n                <p className=\"mt-2 text-lg text-gray-600\">{subtitle}</p>\n              )}\n            </div>\n            \n            {action && (\n              <div className=\"flex-shrink-0\">\n                {action}\n              </div>\n            )}\n          </Flex>\n        </div>\n      </Container>\n    </div>\n  );\n};\n\n// Section Component\nexport const Section = ({ \n  children, \n  title, \n  subtitle,\n  action,\n  spacing = 'default',\n  className = '',\n  ...props \n}) => {\n  const spacings = {\n    sm: 'py-8',\n    default: 'py-12',\n    lg: 'py-16',\n    xl: 'py-20'\n  };\n\n  return (\n    <section className={`${spacings[spacing]} ${className}`} {...props}>\n      <Container>\n        {(title || subtitle || action) && (\n          <div className=\"mb-8\">\n            <Flex justify=\"between\" align=\"start\" className=\"mb-4\">\n              <div>\n                {title && (\n                  <h2 className=\"text-2xl font-bold text-gray-900\">{title}</h2>\n                )}\n                {subtitle && (\n                  <p className=\"mt-2 text-gray-600\">{subtitle}</p>\n                )}\n              </div>\n              {action && <div>{action}</div>}\n            </Flex>\n          </div>\n        )}\n        {children}\n      </Container>\n    </section>\n  );\n};\n\n// Owner Dashboard Layout\nexport const OwnerDashboardLayout = ({ \n  children, \n  sidebar,\n  header,\n  className = ''\n}) => (\n  <div className={`min-h-screen bg-gray-50 ${className}`}>\n    {header}\n    <div className=\"flex\">\n      {sidebar && (\n        <aside className=\"hidden lg:block w-64 bg-white border-r border-gray-200 min-h-screen\">\n          <div className=\"p-6\">\n            {sidebar}\n          </div>\n        </aside>\n      )}\n      <main className=\"flex-1 overflow-hidden\">\n        <div className=\"p-6 lg:p-8\">\n          {children}\n        </div>\n      </main>\n    </div>\n  </div>\n);\n\n// Card Grid Layout\nexport const CardGrid = ({ \n  children, \n  cols = 3,\n  gap = 6,\n  className = ''\n}) => (\n  <Grid cols={cols} gap={gap} className={className}>\n    {children}\n  </Grid>\n);\n\n// Two Column Layout\nexport const TwoColumnLayout = ({ \n  left, \n  right, \n  leftWidth = '2/3',\n  gap = 8,\n  className = ''\n}) => {\n  const widths = {\n    '1/3': 'lg:w-1/3',\n    '1/2': 'lg:w-1/2',\n    '2/3': 'lg:w-2/3',\n    '3/4': 'lg:w-3/4'\n  };\n\n  const rightWidth = leftWidth === '2/3' ? '1/3' : \n                    leftWidth === '1/2' ? '1/2' :\n                    leftWidth === '1/3' ? '2/3' : '1/4';\n\n  return (\n    <div className={`flex flex-col lg:flex-row gap-${gap} ${className}`}>\n      <div className={`${widths[leftWidth]}`}>\n        {left}\n      </div>\n      <div className={`${widths[rightWidth]}`}>\n        {right}\n      </div>\n    </div>\n  );\n};\n\n// Responsive Sidebar Layout\nexport const SidebarLayout = ({ \n  children, \n  sidebar,\n  sidebarPosition = 'left',\n  sidebarWidth = 'w-64',\n  className = ''\n}) => {\n  const isLeft = sidebarPosition === 'left';\n  \n  return (\n    <div className={`flex ${isLeft ? 'flex-row' : 'flex-row-reverse'} ${className}`}>\n      <aside className={`hidden lg:block ${sidebarWidth} flex-shrink-0`}>\n        {sidebar}\n      </aside>\n      <main className=\"flex-1 min-w-0\">\n        {children}\n      </main>\n    </div>\n  );\n};\n\n// Centered Content Layout\nexport const CenteredLayout = ({ \n  children, \n  maxWidth = 'md',\n  className = ''\n}) => {\n  const maxWidths = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl'\n  };\n\n  return (\n    <div className={`min-h-screen flex items-center justify-center p-4 ${className}`}>\n      <div className={`w-full ${maxWidths[maxWidth]}`}>\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default {\n  Container,\n  Grid,\n  Flex,\n  Stack,\n  PageHeader,\n  Section,\n  OwnerDashboardLayout,\n  CardGrid,\n  TwoColumnLayout,\n  SidebarLayout,\n  CenteredLayout\n};\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,IAAI,MAAM,YAAY;;AAE7B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,SAAS,GAAGA,CAAC;EACxBC,QAAQ;EACRC,IAAI,GAAG,SAAS;EAChBC,SAAS,GAAG,EAAE;EACd,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMC,KAAK,GAAG;IACZC,EAAE,EAAE,WAAW;IACfC,OAAO,EAAE,WAAW;IACpBC,EAAE,EAAE,YAAY;IAChBC,IAAI,EAAE;EACR,CAAC;EAED,oBACEV,OAAA;IACEI,SAAS,EAAE,gCAAgCE,KAAK,CAACH,IAAI,CAAC,IAAIC,SAAS,EAAG;IAAA,GAClEC,KAAK;IAAAH,QAAA,EAERA;EAAQ;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAC,EAAA,GAvBad,SAAS;AAwBtB,OAAO,MAAMe,IAAI,GAAGA,CAAC;EACnBd,QAAQ;EACRe,IAAI,GAAG,CAAC;EACRC,GAAG,GAAG,CAAC;EACPd,SAAS,GAAG,EAAE;EACde,UAAU,GAAG,IAAI;EACjB,GAAGd;AACL,CAAC,KAAK;EACJ,MAAMe,QAAQ,GAAG;IACf,CAAC,EAAE,aAAa;IAChB,CAAC,EAAE,4BAA4B;IAC/B,CAAC,EAAE,2CAA2C;IAC9C,CAAC,EAAE,2CAA2C;IAC9C,CAAC,EAAE,0DAA0D;IAC7D,CAAC,EAAE;EACL,CAAC;EAED,MAAMC,eAAe,GAAGF,UAAU,GAAGC,QAAQ,CAACH,IAAI,CAAC,GAAG,aAAaA,IAAI,EAAE;EAEzE,oBACEjB,OAAA;IACEI,SAAS,EAAE,QAAQiB,eAAe,QAAQH,GAAG,IAAId,SAAS,EAAG;IAAA,GACzDC,KAAK;IAAAH,QAAA,EAERA;EAAQ;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAAQ,GAAA,GA7BaN,IAAI;AA8BjB,OAAO,MAAMO,IAAI,GAAGA,CAAC;EACnBrB,QAAQ;EACRsB,SAAS,GAAG,KAAK;EACjBC,KAAK,GAAG,OAAO;EACfC,OAAO,GAAG,OAAO;EACjBC,IAAI,GAAG,KAAK;EACZT,GAAG,GAAG,CAAC;EACPd,SAAS,GAAG,EAAE;EACd,GAAGC;AACL,CAAC,KAAK;EACJ,MAAMuB,UAAU,GAAG;IACjBC,GAAG,EAAE,UAAU;IACfC,GAAG,EAAE,UAAU;IACf,aAAa,EAAE,kBAAkB;IACjC,aAAa,EAAE;EACjB,CAAC;EAED,MAAMC,UAAU,GAAG;IACjBC,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAE,cAAc;IACtBC,GAAG,EAAE,WAAW;IAChBC,OAAO,EAAE,eAAe;IACxBC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMC,cAAc,GAAG;IACrBL,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE,gBAAgB;IACxBC,GAAG,EAAE,aAAa;IAClBI,OAAO,EAAE,iBAAiB;IAC1BC,MAAM,EAAE,gBAAgB;IACxBC,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,SAAS,GAAGd,IAAI,GAAG,WAAW,GAAG,EAAE;EACzC,MAAMe,QAAQ,GAAGxB,GAAG,GAAG,CAAC,GAAG,OAAOA,GAAG,EAAE,GAAG,EAAE;EAE5C,oBACElB,OAAA;IACEI,SAAS,EAAE;AACjB,eAAewB,UAAU,CAACJ,SAAS,CAAC,IAAIO,UAAU,CAACN,KAAK,CAAC;AACzD,UAAUY,cAAc,CAACX,OAAO,CAAC,IAAIe,SAAS,IAAIC,QAAQ,IAAItC,SAAS;AACvE,OAAQ;IAAA,GACEC,KAAK;IAAAH,QAAA,EAERA;EAAQ;IAAAS,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;;AAED;AAAA6B,GAAA,GAlDapB,IAAI;AAmDjB,OAAO,MAAMqB,KAAK,GAAGA,CAAC;EACpB1C,QAAQ;EACR2C,OAAO,GAAG,CAAC;EACXzC,SAAS,GAAG,EAAE;EACd,GAAGC;AACL,CAAC,kBACCL,OAAA;EAAKI,SAAS,EAAE,WAAWyC,OAAO,IAAIzC,SAAS,EAAG;EAAA,GAAKC,KAAK;EAAAH,QAAA,EACzDA;AAAQ;EAAAS,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACN,CACN;;AAED;AAAAgC,GAAA,GAXaF,KAAK;AAYlB,OAAO,MAAMG,UAAU,GAAGA,CAAC;EACzBC,KAAK;EACLC,QAAQ;EACRC,MAAM;EACNC,WAAW;EACXC,OAAO,GAAG,SAAS;EACnBhD,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMiD,QAAQ,GAAG;IACf7C,OAAO,EAAE,mCAAmC;IAC5C8C,KAAK,EAAE,uEAAuE;IAC9EC,OAAO,EAAE;EACX,CAAC;EAED,oBACEvD,OAAA;IAAKI,SAAS,EAAE,GAAGiD,QAAQ,CAACD,OAAO,CAAC,IAAIhD,SAAS,EAAG;IAAAF,QAAA,eAClDF,OAAA,CAACC,SAAS;MAAAC,QAAA,eACRF,OAAA;QAAKI,SAAS,EAAC,cAAc;QAAAF,QAAA,GAC1BiD,WAAW,iBACVnD,OAAA;UAAKI,SAAS,EAAC,MAAM;UAAAF,QAAA,eACnBF,OAAA;YAAII,SAAS,EAAC,qCAAqC;YAAAF,QAAA,EAChDiD,WAAW,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBAC5B1D,OAAA;cAAgBI,SAAS,EAAC,mBAAmB;cAAAF,QAAA,GAC1CwD,KAAK,GAAG,CAAC,iBACR1D,OAAA,CAACF,IAAI;gBAAC6D,IAAI,EAAC,cAAc;gBAACxD,IAAI,EAAE,EAAG;gBAACC,SAAS,EAAC;cAAoB;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CACrE,EACA2C,KAAK,CAACG,IAAI,gBACT5D,OAAA;gBACE4D,IAAI,EAAEH,KAAK,CAACG,IAAK;gBACjBxD,SAAS,EAAE;AACnC;AACA,4BAA4BgD,OAAO,KAAK,OAAO,GAAG,iBAAiB,GAAG,eAAe;AACrF,yBAA0B;gBAAAlD,QAAA,EAEDuD,KAAK,CAACI;cAAK;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,gBAEJd,OAAA;gBAAMI,SAAS,EAAC,2BAA2B;gBAAAF,QAAA,EAAEuD,KAAK,CAACI;cAAK;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAChE;YAAA,GAhBM4C,KAAK;cAAA/C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBV,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CACN,eAEDd,OAAA,CAACuB,IAAI;UAACG,OAAO,EAAC,SAAS;UAACD,KAAK,EAAC,OAAO;UAACrB,SAAS,EAAC,iBAAiB;UAAAF,QAAA,gBAC/DF,OAAA;YAAKI,SAAS,EAAC,gBAAgB;YAAAF,QAAA,gBAC7BF,OAAA;cAAII,SAAS,EAAE;AAC7B;AACA,kBAAkBgD,OAAO,KAAK,OAAO,GACjB,4EAA4E,GAC5E,eAAe;AACnC,eACgB;cAAAlD,QAAA,EACC8C;YAAK;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,EACJmC,QAAQ,iBACPjD,OAAA;cAAGI,SAAS,EAAC,4BAA4B;cAAAF,QAAA,EAAE+C;YAAQ;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACxD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELoC,MAAM,iBACLlD,OAAA;YAAKI,SAAS,EAAC,eAAe;YAAAF,QAAA,EAC3BgD;UAAM;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;;AAED;AAAAgD,GAAA,GAzEaf,UAAU;AA0EvB,OAAO,MAAMgB,OAAO,GAAGA,CAAC;EACtB7D,QAAQ;EACR8C,KAAK;EACLC,QAAQ;EACRC,MAAM;EACNL,OAAO,GAAG,SAAS;EACnBzC,SAAS,GAAG,EAAE;EACd,GAAGC;AACL,CAAC,KAAK;EACJ,MAAM2D,QAAQ,GAAG;IACfzD,EAAE,EAAE,MAAM;IACVC,OAAO,EAAE,OAAO;IAChBC,EAAE,EAAE,OAAO;IACXwD,EAAE,EAAE;EACN,CAAC;EAED,oBACEjE,OAAA;IAASI,SAAS,EAAE,GAAG4D,QAAQ,CAACnB,OAAO,CAAC,IAAIzC,SAAS,EAAG;IAAA,GAAKC,KAAK;IAAAH,QAAA,eAChEF,OAAA,CAACC,SAAS;MAAAC,QAAA,GACP,CAAC8C,KAAK,IAAIC,QAAQ,IAAIC,MAAM,kBAC3BlD,OAAA;QAAKI,SAAS,EAAC,MAAM;QAAAF,QAAA,eACnBF,OAAA,CAACuB,IAAI;UAACG,OAAO,EAAC,SAAS;UAACD,KAAK,EAAC,OAAO;UAACrB,SAAS,EAAC,MAAM;UAAAF,QAAA,gBACpDF,OAAA;YAAAE,QAAA,GACG8C,KAAK,iBACJhD,OAAA;cAAII,SAAS,EAAC,kCAAkC;cAAAF,QAAA,EAAE8C;YAAK;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAC7D,EACAmC,QAAQ,iBACPjD,OAAA;cAAGI,SAAS,EAAC,oBAAoB;cAAAF,QAAA,EAAE+C;YAAQ;cAAAtC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAChD;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EACLoC,MAAM,iBAAIlD,OAAA;YAAAE,QAAA,EAAMgD;UAAM;YAAAvC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN,EACAZ,QAAQ;IAAA;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;;AAED;AAAAoD,GAAA,GAxCaH,OAAO;AAyCpB,OAAO,MAAMI,oBAAoB,GAAGA,CAAC;EACnCjE,QAAQ;EACRkE,OAAO;EACPC,MAAM;EACNjE,SAAS,GAAG;AACd,CAAC,kBACCJ,OAAA;EAAKI,SAAS,EAAE,2BAA2BA,SAAS,EAAG;EAAAF,QAAA,GACpDmE,MAAM,eACPrE,OAAA;IAAKI,SAAS,EAAC,MAAM;IAAAF,QAAA,GAClBkE,OAAO,iBACNpE,OAAA;MAAOI,SAAS,EAAC,qEAAqE;MAAAF,QAAA,eACpFF,OAAA;QAAKI,SAAS,EAAC,KAAK;QAAAF,QAAA,EACjBkE;MAAO;QAAAzD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CACR,eACDd,OAAA;MAAMI,SAAS,EAAC,wBAAwB;MAAAF,QAAA,eACtCF,OAAA;QAAKI,SAAS,EAAC,YAAY;QAAAF,QAAA,EACxBA;MAAQ;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;;AAED;AAAAwD,GAAA,GAzBaH,oBAAoB;AA0BjC,OAAO,MAAMI,QAAQ,GAAGA,CAAC;EACvBrE,QAAQ;EACRe,IAAI,GAAG,CAAC;EACRC,GAAG,GAAG,CAAC;EACPd,SAAS,GAAG;AACd,CAAC,kBACCJ,OAAA,CAACgB,IAAI;EAACC,IAAI,EAAEA,IAAK;EAACC,GAAG,EAAEA,GAAI;EAACd,SAAS,EAAEA,SAAU;EAAAF,QAAA,EAC9CA;AAAQ;EAAAS,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACL,CACP;;AAED;AAAA0D,GAAA,GAXaD,QAAQ;AAYrB,OAAO,MAAME,eAAe,GAAGA,CAAC;EAC9BC,IAAI;EACJC,KAAK;EACLC,SAAS,GAAG,KAAK;EACjB1D,GAAG,GAAG,CAAC;EACPd,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMyE,MAAM,GAAG;IACb,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE,UAAU;IACjB,KAAK,EAAE;EACT,CAAC;EAED,MAAMC,UAAU,GAAGF,SAAS,KAAK,KAAK,GAAG,KAAK,GAC5BA,SAAS,KAAK,KAAK,GAAG,KAAK,GAC3BA,SAAS,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK;EAErD,oBACE5E,OAAA;IAAKI,SAAS,EAAE,iCAAiCc,GAAG,IAAId,SAAS,EAAG;IAAAF,QAAA,gBAClEF,OAAA;MAAKI,SAAS,EAAE,GAAGyE,MAAM,CAACD,SAAS,CAAC,EAAG;MAAA1E,QAAA,EACpCwE;IAAI;MAAA/D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eACNd,OAAA;MAAKI,SAAS,EAAE,GAAGyE,MAAM,CAACC,UAAU,CAAC,EAAG;MAAA5E,QAAA,EACrCyE;IAAK;MAAAhE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAiE,GAAA,GA9BaN,eAAe;AA+B5B,OAAO,MAAMO,aAAa,GAAGA,CAAC;EAC5B9E,QAAQ;EACRkE,OAAO;EACPa,eAAe,GAAG,MAAM;EACxBC,YAAY,GAAG,MAAM;EACrB9E,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAM+E,MAAM,GAAGF,eAAe,KAAK,MAAM;EAEzC,oBACEjF,OAAA;IAAKI,SAAS,EAAE,QAAQ+E,MAAM,GAAG,UAAU,GAAG,kBAAkB,IAAI/E,SAAS,EAAG;IAAAF,QAAA,gBAC9EF,OAAA;MAAOI,SAAS,EAAE,mBAAmB8E,YAAY,gBAAiB;MAAAhF,QAAA,EAC/DkE;IAAO;MAAAzD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACRd,OAAA;MAAMI,SAAS,EAAC,gBAAgB;MAAAF,QAAA,EAC7BA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;;AAED;AAAAsE,GAAA,GArBaJ,aAAa;AAsB1B,OAAO,MAAMK,cAAc,GAAGA,CAAC;EAC7BnF,QAAQ;EACRoF,QAAQ,GAAG,IAAI;EACflF,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMmF,SAAS,GAAG;IAChBhF,EAAE,EAAE,UAAU;IACdiF,EAAE,EAAE,UAAU;IACd/E,EAAE,EAAE,WAAW;IACfwD,EAAE,EAAE;EACN,CAAC;EAED,oBACEjE,OAAA;IAAKI,SAAS,EAAE,qDAAqDA,SAAS,EAAG;IAAAF,QAAA,eAC/EF,OAAA;MAAKI,SAAS,EAAE,UAAUmF,SAAS,CAACD,QAAQ,CAAC,EAAG;MAAApF,QAAA,EAC7CA;IAAQ;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC2E,GAAA,GAnBWJ,cAAc;AAqB3B,eAAe;EACbpF,SAAS;EACTe,IAAI;EACJO,IAAI;EACJqB,KAAK;EACLG,UAAU;EACVgB,OAAO;EACPI,oBAAoB;EACpBI,QAAQ;EACRE,eAAe;EACfO,aAAa;EACbK;AACF,CAAC;AAAC,IAAAtE,EAAA,EAAAO,GAAA,EAAAqB,GAAA,EAAAG,GAAA,EAAAgB,GAAA,EAAAI,GAAA,EAAAI,GAAA,EAAAE,GAAA,EAAAO,GAAA,EAAAK,GAAA,EAAAK,GAAA;AAAAC,YAAA,CAAA3E,EAAA;AAAA2E,YAAA,CAAApE,GAAA;AAAAoE,YAAA,CAAA/C,GAAA;AAAA+C,YAAA,CAAA5C,GAAA;AAAA4C,YAAA,CAAA5B,GAAA;AAAA4B,YAAA,CAAAxB,GAAA;AAAAwB,YAAA,CAAApB,GAAA;AAAAoB,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAX,GAAA;AAAAW,YAAA,CAAAN,GAAA;AAAAM,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}