import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';
import authService from '../../utils/authService';
import CreateProjectModal from '../modals/CreateProjectModal';

const RoleBasedHeader = ({ userRole = 'member', currentUser, currentOrganization }) => {
  const [isOrgDropdownOpen, setIsOrgDropdownOpen] = useState(false);
  const [isProjectDropdownOpen, setIsProjectDropdownOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isNotificationDropdownOpen, setIsNotificationDropdownOpen] = useState(false);
  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  
  const orgDropdownRef = useRef(null);
  const projectDropdownRef = useRef(null);
  const userDropdownRef = useRef(null);
  const mobileMenuRef = useRef(null);
  const notificationDropdownRef = useRef(null);

  // Default user data if not provided
  const user = {
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    avatar: '/assets/images/avatar.jpg',
    role: 'Project Manager',
    ...currentUser
  };

  // Default organization data if not provided
  const organization = currentOrganization || {
    name: 'Acme Corporation',
    domain: 'acme.com',
    logo: '/assets/images/org-logo.png'
  };

  const availableOrganizations = [
    { id: 1, name: 'Acme Corporation', domain: 'acme.com', role: 'Admin' },
    { id: 2, name: 'TechStart Inc', domain: 'techstart.com', role: 'Member' },
    { id: 3, name: 'Global Solutions', domain: 'globalsol.com', role: 'Manager' }
  ];

  // Project state and data
  const [currentProject, setCurrentProject] = useState({
    id: 1,
    name: 'Website Redesign',
    description: 'Complete redesign of company website',
    status: 'active'
  });

  // Mock project data based on user role
  const getAvailableProjects = (role) => {
    const allProjects = [
      { id: 1, name: 'Website Redesign', description: 'Complete redesign of company website', status: 'active', memberRole: 'assigned' },
      { id: 2, name: 'Mobile App Development', description: 'New mobile application for customers', status: 'active', memberRole: 'assigned' },
      { id: 3, name: 'Marketing Campaign', description: 'Q4 marketing campaign planning', status: 'active', memberRole: 'not-assigned' },
      { id: 4, name: 'Database Migration', description: 'Migrate to new database infrastructure', status: 'active', memberRole: 'not-assigned' },
      { id: 5, name: 'Security Audit', description: 'Comprehensive security review', status: 'planning', memberRole: 'not-assigned' }
    ];

    // Members only see projects they're assigned to
    if (role === 'member') {
      return allProjects.filter(project => project.memberRole === 'assigned');
    }

    // Admins and Owners see all projects
    return allProjects;
  };

  const availableProjects = getAvailableProjects(userRole);

  // Role-based navigation configuration
  const getNavigationItems = (role) => {
    const baseItems = [
      { label: 'Projects', path: '/kanban-board', icon: 'Kanban', roles: ['viewer', 'member', 'admin', 'owner'] },
      { label: 'Team Members', path: '/team-members', icon: 'Users', roles: ['member', 'admin', 'owner'] }
    ];

    const adminItems = [
      // Organization settings moved to owner-only items
    ];

    const ownerItems = [
      { label: 'Organization', path: '/organization-settings', icon: 'Settings', roles: ['owner'] },
      { label: 'Analytics', path: '/analytics', icon: 'BarChart3', roles: ['owner'] },
      { label: 'Billing', path: '/billing', icon: 'CreditCard', roles: ['owner'] }
    ];

    // Filter items based on user role
    const allItems = [...baseItems, ...adminItems, ...ownerItems];
    return allItems.filter(item => item.roles.includes(role.toLowerCase()));
  };

  // Get role-specific features
  const getRoleFeatures = (role) => {
    const features = {
      viewer: {
        canCreateProjects: false,
        canInviteMembers: false,
        canManageSettings: false,
        canViewAnalytics: false,
        canManageBilling: false,
        canSwitchOrganizations: false
      },
      member: {
        canCreateProjects: true,
        canInviteMembers: false,
        canManageSettings: false,
        canViewAnalytics: false,
        canManageBilling: false,
        canSwitchOrganizations: false
      },
      admin: {
        canCreateProjects: true,
        canInviteMembers: true,
        canManageSettings: true,
        canViewAnalytics: false,
        canManageBilling: false,
        canSwitchOrganizations: true
      },
      owner: {
        canCreateProjects: true,
        canInviteMembers: true,
        canManageSettings: true,
        canViewAnalytics: true,
        canManageBilling: true,
        canSwitchOrganizations: true
      }
    };
    return features[role.toLowerCase()] || features.viewer;
  };

  const navigationItems = getNavigationItems(userRole);
  const roleFeatures = getRoleFeatures(userRole);

  // Enhanced notification data with comprehensive structure
  const [notifications, setNotifications] = useState([
    {
      id: 'task_001',
      type: 'task_assignment',
      title: 'New Task Assigned',
      message: 'You have been assigned to work on the payment gateway integration for the e-commerce platform.',
      timestamp: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
      isRead: false,
      priority: 'high',
      data: {
        taskId: 'task_123',
        taskTitle: 'Payment Gateway Integration',
        taskDescription: 'Implement secure payment processing with Stripe API for the e-commerce platform. This includes setting up webhooks, handling payment failures, and ensuring PCI compliance.',
        projectName: 'E-commerce Platform',
        projectId: 'proj_001',
        assignerName: 'Emily Davis',
        assignerAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        estimatedHours: 16
      },
      actions: [
        { label: 'Accept Task', action: 'accept_task', variant: 'primary' },
        { label: 'View Details', action: 'view_task', variant: 'secondary' },
        { label: 'Decline', action: 'decline_task', variant: 'danger' }
      ]
    },
    {
      id: 'meeting_001',
      type: 'meeting_invite',
      title: 'Team Sprint Planning',
      message: 'You are invited to the sprint planning meeting for the upcoming development cycle.',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      isRead: false,
      priority: 'medium',
      data: {
        meetingId: 'meet_456',
        meetingTitle: 'Sprint Planning - Q1 2025',
        organizer: 'Sarah Johnson',
        organizerAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
        startTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
        duration: 120, // minutes
        location: 'Conference Room A / Zoom',
        attendees: ['John Doe', 'Alice Smith', 'Bob Wilson'],
        agenda: 'Review previous sprint, plan upcoming tasks, estimate story points'
      },
      actions: [
        { label: 'Accept', action: 'accept_meeting', variant: 'primary' },
        { label: 'Decline', action: 'decline_meeting', variant: 'secondary' },
        { label: 'View Details', action: 'view_meeting', variant: 'secondary' }
      ]
    },
    {
      id: 'ai_001',
      type: 'ai_suggestion',
      title: 'AI Recommendation: Task Prioritization',
      message: 'Based on your current workload and deadlines, I recommend prioritizing the API integration task.',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
      isRead: true,
      priority: 'low',
      data: {
        suggestionType: 'task_prioritization',
        confidence: 0.85,
        reasoning: 'The API integration task is blocking 3 other team members and has a deadline in 2 days.',
        recommendedActions: [
          'Move API integration to top priority',
          'Allocate 6 hours today for this task',
          'Consider requesting help from backend team'
        ],
        impactAnalysis: {
          timesSaved: '4 hours',
          teamEfficiency: '+15%',
          riskReduction: 'High'
        }
      },
      actions: [
        { label: 'Apply Suggestion', action: 'apply_ai_suggestion', variant: 'primary' },
        { label: 'Learn More', action: 'view_ai_details', variant: 'secondary' }
      ]
    },
    {
      id: 'deadline_001',
      type: 'project_update',
      title: 'Deadline Approaching',
      message: 'The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
      isRead: true,
      priority: 'high',
      data: {
        projectId: 'proj_002',
        projectName: 'Mobile App Redesign',
        deadlineDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        completionPercentage: 75,
        remainingTasks: 3,
        assignedTo: 'Design Team'
      },
      actions: [
        { label: 'View Project', action: 'view_project', variant: 'primary' },
        { label: 'Update Status', action: 'update_status', variant: 'secondary' }
      ]
    }
  ]);

  // Notification state and filtering
  const [notificationFilter, setNotificationFilter] = useState('all'); // 'all', 'unread', 'high_priority'
  const [isLoading, setIsLoading] = useState(false);

  const unreadCount = notifications.filter(n => !n.isRead).length;
  const highPriorityCount = notifications.filter(n => n.priority === 'high' && !n.isRead).length;

  // Filter notifications based on current filter
  const filteredNotifications = notifications.filter(notification => {
    switch (notificationFilter) {
      case 'unread':
        return !notification.isRead;
      case 'high_priority':
        return notification.priority === 'high';
      default:
        return true;
    }
  });

  const markAsRead = (notificationId) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === notificationId
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  };

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'task_assignment': return 'UserPlus';
      case 'meeting_invite': return 'Calendar';
      case 'meeting_reminder': return 'Clock';
      case 'ai_suggestion': return 'Zap';
      case 'project_update': return 'AlertTriangle';
      default: return 'Bell';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-destructive';
      case 'medium': return 'text-warning';
      case 'low': return 'text-muted-foreground';
      default: return 'text-muted-foreground';
    }
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return timestamp.toLocaleDateString();
  };

  // Role-based notification filtering
  const getRoleBasedNotifications = (notifications, userRole) => {
    return notifications.filter(notification => {
      switch (notification.type) {
        case 'meeting_invite':
          // All roles can receive meeting invites
          return true;
        case 'ai_suggestion':
          // AI suggestions available to all roles
          return true;
        case 'task_assignment':
          // Task assignments for members and above only
          // Viewers cannot receive task assignments in this system
          return ['member', 'admin', 'owner'].includes(userRole?.toLowerCase());
        case 'project_update':
          // Project updates for all roles
          return true;
        default:
          return true;
      }
    });
  };

  // Notification action handlers
  const handleNotificationAction = async (notificationId, action, notificationData) => {
    setIsLoading(true);

    try {
      switch (action) {
        case 'accept_task':
          // Accept task assignment
          console.log('Accepting task:', notificationData.taskId);
          // Navigate to kanban board with task highlighted
          navigate('/kanban-board', {
            state: {
              projectId: notificationData.projectId,
              highlightTaskId: notificationData.taskId,
              taskAccepted: true
            }
          });
          // Remove notification after acceptance
          setNotifications(prev => prev.filter(n => n.id !== notificationId));
          break;

        case 'view_task':
          // View task details
          navigate('/kanban-board', {
            state: {
              projectId: notificationData.projectId,
              highlightTaskId: notificationData.taskId
            }
          });
          markAsRead(notificationId);
          break;

        case 'decline_task':
          // Decline task assignment
          const reason = prompt('Please provide a reason for declining this task (optional):');
          console.log('Declining task:', notificationData.taskId, 'Reason:', reason);
          // Remove notification after declining
          setNotifications(prev => prev.filter(n => n.id !== notificationId));
          break;

        case 'accept_meeting':
          // Accept meeting invitation
          console.log('Accepting meeting:', notificationData.meetingId);
          // Update meeting status and remove notification
          setNotifications(prev => prev.filter(n => n.id !== notificationId));
          break;

        case 'decline_meeting':
          // Decline meeting invitation
          console.log('Declining meeting:', notificationData.meetingId);
          setNotifications(prev => prev.filter(n => n.id !== notificationId));
          break;

        case 'view_meeting':
          // View meeting details
          console.log('Viewing meeting details:', notificationData.meetingId);
          markAsRead(notificationId);
          break;

        case 'join_meeting':
          // Join meeting (open meeting link)
          console.log('Joining meeting:', notificationData.meetingId);
          window.open('https://zoom.us/j/meeting-room', '_blank');
          markAsRead(notificationId);
          break;

        case 'apply_ai_suggestion':
          // Apply AI suggestion
          console.log('Applying AI suggestion:', notificationData.suggestionType);
          markAsRead(notificationId);
          break;

        case 'view_project':
          // View project details
          navigate('/project-overview', {
            state: {
              projectId: notificationData.projectId
            }
          });
          markAsRead(notificationId);
          break;

        default:
          console.log('Unknown action:', action);
          markAsRead(notificationId);
      }
    } catch (error) {
      console.error('Error handling notification action:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const isActivePath = (path) => location.pathname === path;

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (orgDropdownRef.current && !orgDropdownRef.current.contains(event.target)) {
        setIsOrgDropdownOpen(false);
      }
      if (projectDropdownRef.current && !projectDropdownRef.current.contains(event.target)) {
        setIsProjectDropdownOpen(false);
      }
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {
        setIsUserDropdownOpen(false);
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {
        setIsMobileMenuOpen(false);
      }
      if (notificationDropdownRef.current && !notificationDropdownRef.current.contains(event.target)) {
        setIsNotificationDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleOrganizationSwitch = (orgId) => {
    console.log('Switching to organization:', orgId);
    setIsOrgDropdownOpen(false);
  };

  const handleProjectSwitch = (project) => {
    console.log('Switching to project:', project);
    setCurrentProject(project);
    setIsProjectDropdownOpen(false);
    // Navigate to the project overview page
    navigate('/project-overview', { state: { project: project } });
  };

  const handleCreateProject = () => {
    console.log('Creating new project...');
    setIsProjectDropdownOpen(false);
    setIsCreateProjectModalOpen(true);
  };

  const handleCreateProjectSubmit = async (projectData) => {
    try {
      console.log('Creating project:', projectData);
      // Here you would typically call an API to create the project
      // For now, we'll just add it to the available projects
      const newProject = {
        id: Date.now(),
        name: projectData.name,
        description: projectData.description,
        status: 'active',
        memberRole: 'assigned'
      };

      // Update current project to the newly created one
      setCurrentProject(newProject);
      setIsCreateProjectModalOpen(false);

      // Navigate to the new project's overview page
      navigate('/project-overview', { state: { project: newProject } });
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error;
    }
  };

  const handleLogout = async () => {
    try {
      console.log('Logging out...');

      // Clear authentication state
      await authService.logout();

      // Close any open dropdowns
      setIsUserDropdownOpen(false);
      setIsOrgDropdownOpen(false);
      setIsProjectDropdownOpen(false);
      setIsMobileMenuOpen(false);

      // Redirect to login page
      navigate('/login', { replace: true });
    } catch (error) {
      console.error('Error during logout:', error);
      // Even if logout fails, redirect to login page
      navigate('/login', { replace: true });
    }
  };

  // Don't render header on auth pages
  if (location.pathname === '/login' || location.pathname === '/register') {
    return null;
  }

  // Get role badge color
  const getRoleBadgeColor = (role) => {
    const colors = {
      viewer: 'bg-gray-100 text-gray-800',
      member: 'bg-blue-100 text-blue-800',
      admin: 'bg-purple-100 text-purple-800',
      owner: 'bg-green-100 text-green-800'
    };
    return colors[role.toLowerCase()] || colors.viewer;
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-1000 bg-surface border-b border-border shadow-enterprise">
      <div className="flex items-center justify-between h-16 px-4 lg:px-6">
        {/* Logo */}
        <div className="flex items-center">
          <Link to="/kanban-board" className="flex items-center space-x-3 hover-lift">
            <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
              <svg viewBox="0 0 24 24" className="w-5 h-5 text-primary-foreground" fill="currentColor">
                <path d="M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z"/>
              </svg>
            </div>
            <span className="text-xl font-semibold text-text-primary">Agno WorkSphere</span>
          </Link>
        </div>

        {/* Organization Context Switcher - Desktop */}
        <div className="hidden lg:flex items-center space-x-6">
          {roleFeatures.canSwitchOrganizations ? (
            <div className="relative" ref={orgDropdownRef}>
              <Button
                variant="ghost"
                onClick={() => setIsOrgDropdownOpen(!isOrgDropdownOpen)}
                className="flex items-center space-x-2 px-3 py-2"
              >
                <div className="w-6 h-6 bg-muted rounded-sm flex items-center justify-center">
                  <span className="text-xs font-medium text-text-primary">
                    {organization.name.charAt(0)}
                  </span>
                </div>
                <span className="font-medium text-text-primary">{organization.name}</span>
                <Icon name="ChevronDown" size={16} className="text-text-secondary" />
              </Button>

              {isOrgDropdownOpen && (
                <div className="absolute top-full left-0 mt-1 w-64 bg-popover border border-border rounded-md shadow-elevated z-1010">
                  <div className="p-2">
                    <div className="text-xs font-medium text-text-secondary uppercase tracking-wide px-2 py-1">
                      Switch Organization
                    </div>
                    {availableOrganizations.map((org) => (
                      <button
                        key={org.id}
                        onClick={() => handleOrganizationSwitch(org.id)}
                        className="w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left"
                      >
                        <div className="w-8 h-8 bg-muted rounded-sm flex items-center justify-center">
                          <span className="text-xs font-medium text-text-primary">
                            {org.name.charAt(0)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-text-primary">{org.name}</div>
                        <div className="text-xs text-text-secondary">{org.domain}</div>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded ${getRoleBadgeColor(org.role)}`}>
                        {org.role}
                      </span>
                      {org.id === 1 && (
                        <Icon name="Check" size={16} className="text-success" />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}
            </div>
          ) : (
            // For members and viewers - show organization name without dropdown
            <div className="flex items-center space-x-2 px-3 py-2">
              <div className="w-6 h-6 bg-muted rounded-sm flex items-center justify-center">
                <span className="text-xs font-medium text-text-primary">
                  {organization.name.charAt(0)}
                </span>
              </div>
              <span className="font-medium text-text-primary">{organization.name}</span>
            </div>
          )}

          {/* Role-Based Navigation */}
          <nav className="flex items-center space-x-1">
            {navigationItems.map((item) => {
              // Special handling for Projects item - make it a dropdown
              if (item.label === 'Projects') {
                return (
                  <div key={item.path} className="relative" ref={projectDropdownRef}>
                    <Button
                      variant="ghost"
                      onClick={() => setIsProjectDropdownOpen(!isProjectDropdownOpen)}
                      className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${
                        isActivePath(item.path)
                          ? 'bg-primary text-primary-foreground'
                          : 'text-text-secondary hover:text-text-primary hover:bg-muted'
                      }`}
                    >
                      <Icon name={item.icon} size={16} />
                      <span>{item.label}</span>
                      <Icon name="ChevronDown" size={12} className="ml-1" />
                    </Button>

                    {isProjectDropdownOpen && (
                      <div className="absolute top-full left-0 mt-1 w-80 bg-popover border border-border rounded-md shadow-elevated z-1010">
                        <div className="p-2">
                          <div className="text-xs font-medium text-text-secondary uppercase tracking-wide px-2 py-1">
                            Switch Project
                          </div>

                          {/* Create New Project - Only for Admins and Owners */}
                          {(userRole === 'admin' || userRole === 'owner') && (
                            <>
                              <button
                                onClick={handleCreateProject}
                                className="w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left border-b border-border mb-2"
                              >
                                <div className="w-8 h-8 bg-primary rounded-sm flex items-center justify-center">
                                  <Icon name="Plus" size={16} className="text-primary-foreground" />
                                </div>
                                <div className="flex-1">
                                  <div className="text-sm font-medium text-primary">Create New Project</div>
                                  <div className="text-xs text-text-secondary">Start a new project</div>
                                </div>
                              </button>
                            </>
                          )}

                          {/* Available Projects */}
                          {availableProjects.map((project) => (
                            <button
                              key={project.id}
                              onClick={() => handleProjectSwitch(project)}
                              className="w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left"
                            >
                              <div className="w-8 h-8 bg-muted rounded-sm flex items-center justify-center">
                                <Icon name="Folder" size={16} className="text-text-primary" />
                              </div>
                              <div className="flex-1">
                                <div className="text-sm font-medium text-text-primary">{project.name}</div>
                                <div className="text-xs text-text-secondary truncate">{project.description}</div>
                              </div>
                              <div className="flex flex-col items-end">
                                <span className={`text-xs px-2 py-1 rounded ${
                                  project.status === 'active' ? 'bg-success/10 text-success' :
                                  project.status === 'planning' ? 'bg-warning/10 text-warning' :
                                  'bg-muted text-text-secondary'
                                }`}>
                                  {project.status}
                                </span>
                                {userRole === 'member' && (
                                  <span className="text-xs text-text-secondary mt-1">Assigned</span>
                                )}
                              </div>
                              {project.id === currentProject.id && (
                                <Icon name="Check" size={16} className="text-success" />
                              )}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                );
              }

              // Regular navigation items
              return (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${
                    isActivePath(item.path)
                      ? 'bg-primary text-primary-foreground'
                      : 'text-text-secondary hover:text-text-primary hover:bg-muted'
                  }`}
                >
                  <Icon name={item.icon} size={16} />
                  <span>{item.label}</span>
                </Link>
              );
            })}
          </nav>
        </div>

        {/* User Profile & Mobile Menu */}
        <div className="flex items-center space-x-2">
          {/* Notifications */}
          <div className="relative" ref={notificationDropdownRef}>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsNotificationDropdownOpen(!isNotificationDropdownOpen)}
              className="relative h-9 w-9"
            >
              <Icon name="Bell" size={18} className="text-text-secondary" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 h-5 w-5 bg-destructive text-destructive-foreground text-xs font-medium rounded-full flex items-center justify-center">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </Button>

            {isNotificationDropdownOpen && (
              <div className="absolute top-full right-0 mt-1 w-96 bg-popover border border-border rounded-md shadow-elevated z-1010 md:w-80">
                {/* Header with filters */}
                <div className="p-4 border-b border-border">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-text-primary flex items-center">
                      Notifications
                      {unreadCount > 0 && (
                        <span className="ml-2 bg-destructive text-destructive-foreground text-xs px-2 py-1 rounded-full">
                          {unreadCount}
                        </span>
                      )}
                    </h3>
                    {unreadCount > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={markAllAsRead}
                        className="text-xs text-primary hover:text-primary"
                      >
                        Mark all read
                      </Button>
                    )}
                  </div>

                  {/* Filter tabs */}
                  <div className="flex space-x-1">
                    <button
                      onClick={() => setNotificationFilter('all')}
                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                        notificationFilter === 'all'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted text-text-secondary hover:bg-muted/80'
                      }`}
                    >
                      All {notifications.length > 0 && <span className="ml-1">{notifications.length}</span>}
                    </button>
                    <button
                      onClick={() => setNotificationFilter('unread')}
                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                        notificationFilter === 'unread'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted text-text-secondary hover:bg-muted/80'
                      }`}
                    >
                      Unread {unreadCount > 0 && <span className="ml-1">{unreadCount}</span>}
                    </button>
                    <button
                      onClick={() => setNotificationFilter('high_priority')}
                      className={`px-3 py-1 text-xs rounded-full transition-colors ${
                        notificationFilter === 'high_priority'
                          ? 'bg-primary text-primary-foreground'
                          : 'bg-muted text-text-secondary hover:bg-muted/80'
                      }`}
                    >
                      High Priority {highPriorityCount > 0 && <span className="ml-1">{highPriorityCount}</span>}
                    </button>
                  </div>
                </div>

                {/* Notifications list */}
                <div className="max-h-96 overflow-y-auto">
                  {filteredNotifications.length === 0 ? (
                    <div className="p-6 text-center text-text-secondary">
                      <Icon name="Bell" size={32} className="mx-auto mb-3 opacity-50" />
                      <p className="font-medium">No notifications</p>
                      <p className="text-sm mt-1">
                        {notificationFilter === 'unread' ? 'All caught up!' :
                         notificationFilter === 'high_priority' ? 'No high priority items' :
                         'You\'re all set'}
                      </p>
                    </div>
                  ) : (
                    getRoleBasedNotifications(filteredNotifications, userRole).map((notification) => (
                      <div
                        key={notification.id}
                        className={`p-4 border-b border-border last:border-b-0 hover:bg-muted/30 transition-colors ${
                          !notification.isRead ? 'bg-primary/5 border-l-4 border-l-primary' : ''
                        }`}
                      >
                        <div className="flex items-start space-x-3">
                          {/* Notification icon */}
                          <div className={`p-2 rounded-full flex-shrink-0 ${
                            notification.type === 'task_assignment' ? 'bg-blue-100 text-blue-600' :
                            notification.type === 'meeting_invite' ? 'bg-green-100 text-green-600' :
                            notification.type === 'meeting_reminder' ? 'bg-orange-100 text-orange-600' :
                            notification.type === 'ai_suggestion' ? 'bg-purple-100 text-purple-600' :
                            notification.type === 'project_update' ? 'bg-red-100 text-red-600' :
                            'bg-gray-100 text-gray-600'
                          }`}>
                            <Icon name={getNotificationIcon(notification.type)} size={14} />
                          </div>

                          <div className="flex-1 min-w-0">
                            {/* Header */}
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex-1">
                                <div className="flex items-center space-x-2">
                                  <p className={`text-sm truncate ${!notification.isRead ? 'font-semibold text-text-primary' : 'font-medium text-text-primary'}`}>
                                    {notification.title}
                                  </p>
                                  <span className={`text-xs px-1.5 py-0.5 rounded ${getPriorityColor(notification.priority)} bg-muted`}>
                                    {notification.priority}
                                  </span>
                                </div>
                                <p className="text-text-secondary text-xs mt-1">
                                  {formatTimestamp(notification.timestamp)}
                                </p>
                              </div>
                              {!notification.isRead && (
                                <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0 ml-2 mt-1"></div>
                              )}
                            </div>

                            {/* Message */}
                            <p className="text-text-secondary text-sm mb-3 line-clamp-2">
                              {notification.message}
                            </p>

                            {/* Type-specific content */}
                            {notification.type === 'task_assignment' && notification.data && (
                              <div className="mb-3 p-2 bg-muted/50 rounded-md">
                                <div className="flex items-center space-x-2 mb-2">
                                  <img
                                    src={notification.data.assignerAvatar}
                                    alt={notification.data.assignerName}
                                    className="w-5 h-5 rounded-full"
                                  />
                                  <span className="text-xs text-text-secondary">
                                    Assigned by {notification.data.assignerName}
                                  </span>
                                </div>
                                <p className="text-xs text-text-secondary">
                                  Project: {notification.data.projectName}
                                </p>
                                {notification.data.dueDate && (
                                  <p className="text-xs text-text-secondary">
                                    Due: {notification.data.dueDate.toLocaleDateString()}
                                  </p>
                                )}
                              </div>
                            )}

                            {notification.type === 'meeting_invite' && notification.data && (
                              <div className="mb-3 p-2 bg-muted/50 rounded-md">
                                <div className="flex items-center space-x-2 mb-1">
                                  <img
                                    src={notification.data.organizerAvatar}
                                    alt={notification.data.organizer}
                                    className="w-5 h-5 rounded-full"
                                  />
                                  <span className="text-xs text-text-secondary">
                                    Organized by {notification.data.organizer}
                                  </span>
                                </div>
                                <p className="text-xs text-text-secondary">
                                  {notification.data.startTime.toLocaleDateString()} at {notification.data.startTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                                </p>
                                <p className="text-xs text-text-secondary">
                                  Duration: {notification.data.duration} minutes
                                </p>
                              </div>
                            )}

                            {/* Action buttons */}
                            {notification.actions && notification.actions.length > 0 && (
                              <div className="flex flex-wrap gap-2">
                                {notification.actions.map((action, index) => (
                                  <Button
                                    key={index}
                                    variant={action.variant === 'primary' ? 'default' : action.variant === 'danger' ? 'destructive' : 'outline'}
                                    size="sm"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleNotificationAction(notification.id, action.action, notification.data);
                                    }}
                                    disabled={isLoading}
                                    className="text-xs h-7"
                                  >
                                    {action.label}
                                  </Button>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>

                {/* Footer */}
                <div className="p-3 border-t border-border">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-primary hover:text-primary"
                    onClick={() => {
                      setIsNotificationDropdownOpen(false);
                      // Navigate to full notifications page if it exists
                      console.log('Navigate to full notifications page');
                    }}
                  >
                    View all notifications
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* User Profile Dropdown */}
          <div className="relative" ref={userDropdownRef}>
            <Button
              variant="ghost"
              onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
              className="flex items-center space-x-2 px-2 py-2"
            >
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-primary-foreground">
                  {user?.name ? user.name.split(' ').map(n => n[0]).join('') : 'U'}
                </span>
              </div>
              <div className="hidden md:block text-left">
                <div className="text-sm font-medium text-text-primary">{user?.name || 'User'}</div>
                <div className="text-xs text-text-secondary">{user?.role || 'Member'}</div>
              </div>
              <Icon name="ChevronDown" size={16} className="text-text-secondary" />
            </Button>

            {isUserDropdownOpen && (
              <div className="absolute top-full right-0 mt-1 w-56 bg-popover border border-border rounded-md shadow-elevated z-1010">
                <div className="p-2">
                  <div className="px-2 py-2 border-b border-border">
                    <div className="font-medium text-text-primary">{user?.name || 'User'}</div>
                    <div className="text-sm text-text-secondary">{user?.email || '<EMAIL>'}</div>
                    <span className={`inline-block text-xs px-2 py-1 rounded mt-1 ${getRoleBadgeColor(userRole)}`}>
                      {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
                    </span>
                  </div>
                  <div className="py-1">
                    <Link 
                      to="/user-profile-settings"
                      className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro"
                    >
                      <Icon name="User" size={16} />
                      <span>Profile Settings</span>
                    </Link>
                    <button className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro">
                      <Icon name="Bell" size={16} />
                      <span>Notifications</span>
                    </button>
                    <button className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro">
                      <Icon name="HelpCircle" size={16} />
                      <span>Help & Support</span>
                    </button>
                    <div className="border-t border-border my-1"></div>
                    <button 
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-destructive hover:bg-muted rounded-sm transition-micro"
                    >
                      <Icon name="LogOut" size={16} />
                      <span>Sign Out</span>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Mobile Menu Toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden"
          >
            <Icon name={isMobileMenuOpen ? "X" : "Menu"} size={20} />
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div ref={mobileMenuRef} className="lg:hidden border-t border-border bg-surface">
          <div className="px-4 py-2 space-y-1">
            {/* Create Project Button for Mobile */}
            {roleFeatures.canCreateProjects && (
              <button
                onClick={() => {
                  navigate('/project-management');
                  setIsMobileMenuOpen(false);
                }}
                className="w-full flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-micro"
              >
                <Icon name="Plus" size={16} />
                <span>New Project</span>
              </button>
            )}

            {navigationItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                onClick={() => setIsMobileMenuOpen(false)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${
                  isActivePath(item.path)
                    ? 'bg-primary text-primary-foreground'
                    : 'text-text-secondary hover:text-text-primary hover:bg-muted'
                }`}
              >
                <Icon name={item.icon} size={16} />
                <span>{item.label}</span>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={isCreateProjectModalOpen}
        onClose={() => setIsCreateProjectModalOpen(false)}
        onCreateProject={handleCreateProjectSubmit}
        organizationId={organization.id || 1}
        organizationName={organization.name}
      />
    </header>
  );
};

export default RoleBasedHeader;
